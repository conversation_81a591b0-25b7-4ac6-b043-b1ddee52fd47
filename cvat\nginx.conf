worker_processes auto;
pid /tmp/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
  worker_connections 512;
}

error_log /home/<USER>/logs/nginx_error.log;

http {

  ##
  # Basic Settings
  ##

  sendfile on;
  tcp_nopush on;
  types_hash_max_size 2048;

  include /etc/nginx/mime.types;
  default_type application/octet-stream;

  ##
  # Override default tmp paths to run nginx from non-root user
  ##

  proxy_temp_path    /tmp/nginx_proxy;
  uwsgi_temp_path    /tmp/nginx_uwsgi;
  scgi_temp_path     /tmp/nginx_scgi;
  fastcgi_temp_path  /tmp/nginx_fastcgi;
  client_body_temp_path /tmp/nginx_body;

  ##
  # Logging Settings
  ##

  access_log /home/<USER>/logs/nginx_access.log;

  ##
  # CVAT Settings
  ##

  server {
    listen 8080;
    # previously used value
    client_max_body_size 1G;

    add_header X-Frame-Options deny;

    server_name _;

    location /static/ {
      gzip on;
      gzip_comp_level 6;
      gzip_http_version 1.1;
      gzip_types
          application/javascript
          application/x-javascript
          text/javascript
          application/wasm
          image/x-icon;

      alias /home/<USER>/static/;
    }

    location /data/ {
      internal;
      alias /home/<USER>/data/;
    }

    location / {
      proxy_set_header Host $http_host;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection $connection_upgrade;
      proxy_redirect off;
      proxy_buffering on;
      proxy_buffers 16 8k;
      proxy_temp_path /tmp/nginx 1 2;
      proxy_pass http://uvicorn;
    }
  }

  map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
  }

  upstream uvicorn {
    server unix:/tmp/uvicorn.sock;
  }
}
