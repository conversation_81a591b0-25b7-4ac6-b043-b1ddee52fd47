# https://help.github.com/en/github/creating-cloning-and-archiving-repositories/about-code-owners

# These owners will be the default owners for everything in
# the repo. Unless a later match takes precedence, they will
# be requested for review when someone opens a pull request.
*               @nmanovic

# Order is important; the last matching pattern takes the most
# precedence. When someone opens a pull request that only
# modifies components below, only the list of owners and not
# the global owner(s) will be requested for a review.

# Component: Server
/cvat/           @Marishka17

# Component: CVAT SDK/CLI
/cvat-sdk/       @SpecLad
/cvat/schema.yml @SpecLad
/cvat-cli/       @SpecLad

# Component: Documentation
/site/          @mdacoca
/CHANGELOG.md   @mdacoca
/README.md      @mdacoca

# Component: CVAT UI
/cvat-ui/       @bsekachev
/cvat-data/     @azhavoro
/cvat-canvas/   @bsekachev
/cvat-canvas3d/ @bsekachev
/cvat-core/     @bsekachev

# Component: Da<PERSON><PERSON>
/datumaro/      @zhiltsov-max
/cvat/apps/dataset_manager/ @zhiltsov-max

# Advanced components (e.g. analytics)
/components/    @azhavoro

# Component: Tests
/tests/         @kirill-sizov

# Component: Serverless functions
/serverless/    @kirill-sizov

# Infrastructure
Dockerfile*     @azhavoro
docker-compose* @azhavoro
.*              @azhavoro
*.conf          @azhavoro
*.sh            @azhavoro
/cvat_proxy/    @azhavoro
/tests/         @azhavoro
/utils/         @azhavoro
/LICENSE        @nmanovic
/.github/       @kirill-sizov
/helm-chart/    @azhavoro
