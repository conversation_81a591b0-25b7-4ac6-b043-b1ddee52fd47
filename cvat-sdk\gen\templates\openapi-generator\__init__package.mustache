{{>partial_header}}

__version__ = "{{packageVersion}}"

from {{packageName}}.api_client import ApiClient

from {{packageName}}.configuration import Configuration
{{#hasHttpSignatureMethods}}
from {{packageName}}.signing import HttpSigningConfiguration
{{/hasHttpSignatureMethods}}

from {{packageName}}.exceptions import OpenApiException
from {{packageName}}.exceptions import ApiAttributeError
from {{packageName}}.exceptions import ApiTypeError
from {{packageName}}.exceptions import ApiValueError
from {{packageName}}.exceptions import ApiKeyError
from {{packageName}}.exceptions import ApiException
{{#recursionLimit}}

__import__('sys').setrecursionlimit({{{.}}})
{{/recursionLimit}}
