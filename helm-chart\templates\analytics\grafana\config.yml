{{- if .Values.analytics.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  # vector helm doesn't allow template name of CM
  name: cvat-grafana-dashboards
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "cvat.labels" . | nindent 4 }}
    app: cvat-app
    tier: grafana
{{- if semverCompare ">=1.21-0" .Capabilities.KubeVersion.GitVersion }}
immutable: false
{{- end }}
data:
{{ (.Files.Glob "analytics/grafana/dashboards/*.json").AsConfig | indent 2 }}
{{- end}}
