{{>partial_header}}

# import all models into this package
# if you have many models here with many references from one model to another this may
# raise a RecursionError
# to avoid this, import only the models that you directly need like:
# from from {{modelPackage}}.pet import Pet
# or import this package, but before doing it, use:
# import sys
# sys.setrecursionlimit(n)

{{#models}}
{{#model}}
from {{modelPackage}}.{{classFilename}} import {{classname}}{{^interfaces}}, I{{classname}}{{/interfaces}}
{{/model}}
{{/models}}
