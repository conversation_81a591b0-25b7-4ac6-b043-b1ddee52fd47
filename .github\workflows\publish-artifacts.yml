name: Publish artifacts
on:
  release:
    types: [released]

jobs:
  docker-images:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Build images
        run: |
          CVAT_VERSION=latest CLAM_AV=yes docker compose -f docker-compose.yml -f docker-compose.dev.yml build

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Push to Docker Hub
        env:
          DOCKERHUB_WORKSPACE: ${{ secrets.DOCKERHUB_WORKSPACE }}
          SERVER_IMAGE_REPO: 'server'
          UI_IMAGE_REPO: 'ui'
        run: |
          docker tag "${DOCKERHUB_WORKSPACE}/${SERVER_IMAGE_REPO}:latest" "${DOCKERHUB_WORKSPACE}/${SERVER_IMAGE_REPO}:${{ github.event.release.tag_name }}"
          docker push "${DOCKERHUB_WORKSPACE}/${SERVER_IMAGE_REPO}:${{ github.event.release.tag_name }}"
          docker push "${DOCKERHUB_WORKSPACE}/${SERVER_IMAGE_REPO}:latest"

          docker tag "${DOCKERHUB_WORKSPACE}/${UI_IMAGE_REPO}:latest" "${DOCKERHUB_WORKSPACE}/${UI_IMAGE_REPO}:${{ github.event.release.tag_name }}"
          docker push "${DOCKERHUB_WORKSPACE}/${UI_IMAGE_REPO}:${{ github.event.release.tag_name }}"
          docker push "${DOCKERHUB_WORKSPACE}/${UI_IMAGE_REPO}:latest"

  python-packages:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    steps:
      - uses: actions/checkout@v4

      - name: Generate SDK
        run: |
          pip3 install --user -r cvat-sdk/gen/requirements.txt
          ./cvat-sdk/gen/generate.sh

      - name: Build packages
        run: |
          for d in cvat-sdk cvat-cli; do
            pipx run --spec=build pyproject-build --outdir=dist "$d"
          done

      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
