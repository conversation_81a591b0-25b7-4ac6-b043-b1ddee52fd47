# SHA1:324d0ff72d2920a7d7c735fd480ddb30424c06ec
#
# This file is autogenerated by pip-compile-multi
# To update, run:
#
#    pip-compile-multi
#
-r ../../utils/dataset_manifest/requirements.txt
asgiref==3.8.1
    # via django
async-timeout==4.0.3
    # via redis
attrs==21.4.0
    # via
    #   -r cvat/requirements/base.in
    #   datumaro
    #   jsonschema
azure-core==1.30.1
    # via
    #   azure-storage-blob
    #   msrest
azure-storage-blob==12.13.0
    # via -r cvat/requirements/base.in
boto3==1.17.61
    # via -r cvat/requirements/base.in
botocore==1.20.112
    # via
    #   boto3
    #   s3transfer
cachetools==5.3.3
    # via google-auth
certifi==2024.2.2
    # via
    #   clickhouse-connect
    #   msrest
    #   requests
cffi==1.16.0
    # via cryptography
charset-normalizer==3.3.2
    # via requests
click==8.1.7
    # via rq
clickhouse-connect==0.6.8
    # via -r cvat/requirements/base.in
contourpy==1.2.1
    # via matplotlib
coreapi==2.3.3
    # via -r cvat/requirements/base.in
coreschema==0.0.4
    # via coreapi
crontab==1.0.1
    # via rq-scheduler
cryptography==42.0.5
    # via
    #   azure-storage-blob
    #   pyjwt
cycler==0.12.1
    # via matplotlib
datumaro @ git+https://github.com/cvat-ai/datumaro.git@82982b16b178eb3f39c707795bb68a3306610abf
    # via -r cvat/requirements/base.in
defusedxml==0.7.1
    # via
    #   datumaro
    #   python3-openid
deprecated==1.2.14
    # via limits
dj-pagination==2.5.0
    # via -r cvat/requirements/base.in
dj-rest-auth[with_social]==5.0.2
    # via -r cvat/requirements/base.in
django==4.2.11
    # via
    #   -r cvat/requirements/base.in
    #   dj-rest-auth
    #   django-allauth
    #   django-appconf
    #   django-auth-ldap
    #   django-cors-headers
    #   django-crum
    #   django-filter
    #   django-health-check
    #   django-rq
    #   django-sendfile2
    #   djangorestframework
    #   drf-spectacular
django-allauth==0.57.2
    # via
    #   -r cvat/requirements/base.in
    #   dj-rest-auth
django-appconf==1.0.6
    # via django-compressor
django-auth-ldap==2.2.0
    # via -r cvat/requirements/base.in
django-compressor==4.3.1
    # via -r cvat/requirements/base.in
django-cors-headers==3.5.0
    # via -r cvat/requirements/base.in
django-crum==0.7.9
    # via -r cvat/requirements/base.in
django-filter==2.4.0
    # via -r cvat/requirements/base.in
django-health-check==3.18.1
    # via -r cvat/requirements/base.in
django-rq==2.8.1
    # via -r cvat/requirements/base.in
django-sendfile2==0.7.0
    # via -r cvat/requirements/base.in
djangorestframework==3.14.0
    # via
    #   -r cvat/requirements/base.in
    #   dj-rest-auth
    #   drf-spectacular
drf-spectacular==0.26.2
    # via -r cvat/requirements/base.in
easyprocess==1.1
    # via pyunpack
entrypoint2==1.1
    # via pyunpack
fonttools==4.50.0
    # via matplotlib
freezegun==1.4.0
    # via rq-scheduler
furl==2.1.0
    # via -r cvat/requirements/base.in
google-api-core==2.18.0
    # via
    #   google-cloud-core
    #   google-cloud-storage
google-auth==2.29.0
    # via
    #   google-api-core
    #   google-cloud-core
    #   google-cloud-storage
google-cloud-core==2.4.1
    # via google-cloud-storage
google-cloud-storage==1.42.0
    # via -r cvat/requirements/base.in
google-crc32c==1.5.0
    # via google-resumable-media
google-resumable-media==2.7.0
    # via google-cloud-storage
googleapis-common-protos==1.63.0
    # via google-api-core
h5py==3.10.0
    # via datumaro
idna==3.6
    # via requests
importlib-metadata==7.1.0
    # via clickhouse-connect
importlib-resources==6.4.0
    # via limits
inflection==0.5.1
    # via drf-spectacular
isodate==0.6.1
    # via msrest
itypes==1.2.0
    # via coreapi
jinja2==3.1.3
    # via coreschema
jmespath==0.10.0
    # via
    #   boto3
    #   botocore
jsonschema==4.17.3
    # via drf-spectacular
kiwisolver==1.4.5
    # via matplotlib
limits==3.10.1
    # via python-logstash-async
lxml==5.2.1
    # via datumaro
lz4==4.3.3
    # via clickhouse-connect
markupsafe==2.1.5
    # via jinja2
matplotlib==3.8.4
    # via
    #   datumaro
    #   pycocotools
msrest==0.7.1
    # via azure-storage-blob
networkx==3.2.1
    # via datumaro
nibabel==5.2.1
    # via datumaro
oauthlib==3.2.2
    # via requests-oauthlib
orderedmultidict==1.0.1
    # via furl
orjson==3.10.0
    # via datumaro
packaging==24.0
    # via
    #   limits
    #   matplotlib
    #   nibabel
    #   tensorboardx
pandas==2.2.1
    # via datumaro
patool==1.12
    # via -r cvat/requirements/base.in
pdf2image==1.14.0
    # via -r cvat/requirements/base.in
proto-plus==1.23.0
    # via google-api-core
protobuf==4.25.3
    # via
    #   google-api-core
    #   googleapis-common-protos
    #   proto-plus
    #   tensorboardx
psutil==5.9.4
    # via -r cvat/requirements/base.in
psycopg2-binary==2.9.5
    # via -r cvat/requirements/base.in
pyasn1==0.6.0
    # via
    #   pyasn1-modules
    #   python-ldap
    #   rsa
pyasn1-modules==0.4.0
    # via
    #   google-auth
    #   python-ldap
pycocotools==2.0.7
    # via datumaro
pycparser==2.22
    # via cffi
pyjwt[crypto]==2.8.0
    # via django-allauth
pylogbeat==2.0.1
    # via python-logstash-async
pyparsing==3.1.2
    # via matplotlib
pyrsistent==0.20.0
    # via jsonschema
python-dateutil==2.9.0.post0
    # via
    #   botocore
    #   freezegun
    #   matplotlib
    #   pandas
    #   rq-scheduler
python-ldap==3.4.3
    # via
    #   -r cvat/requirements/base.in
    #   django-auth-ldap
python-logstash-async==2.5.0
    # via -r cvat/requirements/base.in
python3-openid==3.2.0
    # via django-allauth
pytz==2024.1
    # via
    #   clickhouse-connect
    #   djangorestframework
    #   pandas
pyunpack==0.2.1
    # via -r cvat/requirements/base.in
pyyaml==6.0.1
    # via
    #   datumaro
    #   drf-spectacular
rcssmin==1.1.1
    # via django-compressor
redis==4.5.4
    # via
    #   -r cvat/requirements/base.in
    #   django-rq
    #   rq
requests==2.31.0
    # via
    #   -r cvat/requirements/base.in
    #   azure-core
    #   coreapi
    #   datumaro
    #   django-allauth
    #   google-api-core
    #   google-cloud-storage
    #   msrest
    #   python-logstash-async
    #   requests-oauthlib
requests-oauthlib==2.0.0
    # via
    #   django-allauth
    #   msrest
rjsmin==1.2.1
    # via django-compressor
rq==1.16.0
    # via
    #   -r cvat/requirements/base.in
    #   django-rq
    #   rq-scheduler
rq-scheduler==0.13.1
    # via -r cvat/requirements/base.in
rsa==4.9
    # via google-auth
ruamel-yaml==0.18.6
    # via datumaro
ruamel-yaml-clib==0.2.8
    # via ruamel-yaml
rules==3.3
    # via -r cvat/requirements/base.in
s3transfer==0.4.2
    # via boto3
scipy==1.13.0
    # via datumaro
shapely==1.7.1
    # via -r cvat/requirements/base.in
six==1.16.0
    # via
    #   azure-core
    #   furl
    #   isodate
    #   orderedmultidict
    #   python-dateutil
sqlparse==0.4.4
    # via django
tensorboardx==*******
    # via datumaro
typing-extensions==4.10.0
    # via
    #   asgiref
    #   azure-core
    #   datumaro
    #   limits
tzdata==2024.1
    # via pandas
uritemplate==4.1.1
    # via
    #   coreapi
    #   drf-spectacular
urllib3==1.26.18
    # via
    #   botocore
    #   clickhouse-connect
    #   requests
wrapt==1.16.0
    # via deprecated
zipp==3.18.1
    # via importlib-metadata
zstandard==0.22.0
    # via clickhouse-connect
