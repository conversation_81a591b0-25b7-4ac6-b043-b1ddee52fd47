# Generated by Django 2.2.13 on 2020-08-13 05:49

from cvat.apps.engine.media_extractors import _is_archive, _is_zip
import cvat.apps.engine.models
from django.conf import settings
from django.db import migrations, models
import os
from pyunpack import Archive

def unzip(apps, schema_editor):
    Data = apps.get_model("engine", "Data")
    data_q_set = Data.objects.all()
    archive_paths = []

    for data_instance in data_q_set:
        for root, _, files in os.walk(os.path.join(settings.MEDIA_DATA_ROOT, '{}/raw/'.format(data_instance.id))):
            archive_paths.extend([os.path.join(root, file) for file in files if _is_archive(file) or _is_zip(file)])

    for path in archive_paths:
        Archive(path).extractall(os.path.dirname(path))
        os.remove(path)

class Migration(migrations.Migration):

    dependencies = [
        ('engine', '0028_labelcolor'),
    ]

    operations = [
        migrations.AddField(
            model_name='data',
            name='storage_method',
            field=models.CharField(choices=[('cache', 'CACHE'), ('file_system', 'FILE_SYSTEM')], default=cvat.apps.engine.models.StorageMethodChoice('file_system'), max_length=15),
        ),
        migrations.RunPython(unzip),
    ]
