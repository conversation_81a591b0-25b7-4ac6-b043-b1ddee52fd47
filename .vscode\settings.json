{
    "eslint.probe": [
        "javascript",
        "typescript",
        "typescriptreact"
    ],
    "eslint.onIgnoredFiles": "warn",
    "eslint.workingDirectories": [
        {
            "directory": "${cwd}",
        },
        {
            "pattern":  "cvat-*"
        },
        {
            "directory": "tests",
            "!cwd": true
        }
    ],
    "npm.exclude": "**/.env/**",
    "licenser.license": "Custom",
    "licenser.customHeader": "Copyright (C) @YEAR@ CVAT.ai Corporation\n\nSPDX-License-Identifier: MIT",
    "files.trimTrailingWhitespace": true,
    "sqltools.connections": [
        {
            "previewLimit": 50,
            "driver": "SQLite",
            "name": "cvat",
            "database": "${workspaceFolder:cvat}/db.sqlite3"
        }
    ],
    "python.defaultInterpreterPath": "${workspaceFolder}/.env/",
    "python.testing.pytestArgs": [
        "--rootdir","${workspaceFolder}/tests/"
    ],
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,
    "python.testing.pytestPath": "${workspaceFolder}/.env/bin/pytest",
    "python.testing.cwd": "${workspaceFolder}/tests",
    "cSpell.words": [
        "crowdsourcing"
    ]
}
