
class I{{classname}}(IModelData):
    """
    NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    value: {{> model_templates/type_annotation_cleaned}}
    """{{^required}}
    [optional{{#defaultValue}}, default: {{{.}}}{{/defaultValue}}]{{/required}}

    {{#allowableValues}}One of: {{#enumVars}}{{#-first}}{{{value}}}{{/-first}}{{^-first}}, {{{value}}}{{/-first}}{{/enumVars}}{{/allowableValues}}  # noqa: E501
    """

class {{classname}}(ModelSimple, I{{classname}}):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.

    Attributes:
{{> model_templates/docstring_allowed }}
{{> model_templates/docstring_openapi_validations }}
    """

{{> model_templates/classvars }}

    attribute_map = {}

    read_only_vars = set()

    _composed_schemas = None

{{> model_templates/method_init_simple}}

{{> model_templates/method_from_openapi_data_simple}}