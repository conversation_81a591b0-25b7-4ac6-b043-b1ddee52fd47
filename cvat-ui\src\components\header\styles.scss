// Copyright (C) 2020-2022 Intel Corporation
//
// SPDX-License-Identifier: MIT

@import '../../base';

.cvat-header.ant-layout-header {
    display: flex;
    padding-left: 0;
    padding-right: 0;
    line-height: 0;
    height: $header-height;
    background: $header-color;
}

.ant-btn.cvat-header-button {
    color: $text-color;
    padding: 0 $grid-unit-size;
    margin-right: $grid-unit-size;
}

.cvat-left-header {
    .ant-btn.cvat-header-button {
        opacity: 0.7;

        &.cvat-active-header-button {
            font-weight: bold;
            opacity: 1;
        }
    }

    .anticon.cvat-logo-icon {
        margin: 0 $grid-unit-size * 2;
        font-size: 32px; // 控制logo大小，可以根据需要调整
        width: 32px;     // 设置宽度
        height: 32px;    // 设置高度
    }

    width: 50%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.cvat-right-header {
    width: 50%;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    > a.ant-btn {
        span[role='img'] {
            font-size: 24px;
            line-height: 24px;
        }
    }
}

.cvat-header-menu-user-dropdown {
    display: flex;
    align-items: center;
    border-left: 1px solid $border-color-1;

    .anticon.cvat-header-dropdown-icon {
        &.anticon-caret-down {
            font-size: 12px;
        }

        font-size: 20px;
        padding: $grid-unit-size;
    }

    > div:nth-child(2) {
        > div:nth-child(2) { /* org slug */
            font-size: 10px;
        }

        max-width: $grid-unit-size * 15;
        height: $grid-unit-size * 5;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        align-items: center;
    }
}

.cvat-header-menu {
    width: $grid-unit-size * 20;
}

.cvat-header-menu-active-organization-item {
    ::after {
        content: ' \2713';
        float: right;
        margin-left: $grid-unit-size;
    }

    font-weight: bold;
}

.cvat-modal-organization-selector {
    width: 100%;
}

.cvat-shortcuts-modal-window-table {
    .ant-table {
        max-height: $grid-unit-size * 70;
        overflow-y: auto;
    }
}
