name: Helm
on:
  push:
    branches:
      - 'master'
      - 'develop'
  pull_request:
    types: [ready_for_review, opened, synchronize, reopened]
    paths-ignore:
      - 'site/**'
      - '**/*.md'
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  testing:
    if: |
      github.event.pull_request.draft == false &&
      !startsWith(github.event.pull_request.title, '[WIP]') &&
      !startsWith(github.event.pull_request.title, '[Dependent]')
    runs-on: ubuntu-latest-big
    steps:
    - uses: actions/checkout@v4

    - name: Start minikube
      uses: medyagh/setup-minikube@latest
      with:
        cpus: max
        memory: max

    - name: Try the cluster!
      run: kubectl get pods -A

    - name: Build images
      run: |
        export SHELL=/bin/bash
        eval $(minikube -p minikube docker-env)
        docker compose -f docker-compose.yml -f docker-compose.dev.yml build
        echo -n "verifying images:"
        docker images

    - uses: azure/setup-helm@v3
      with:
        version: 'v3.9.4'

    - name: Deploy to minikube
      run: |
        printf "  service:\n    externalIPs:\n      - $(minikube ip)\n" >> helm-chart/test.values.yaml
        cd helm-chart
        helm dependency update
        cd ..
        helm upgrade -n default release-${{ github.run_id }}-${{ github.run_attempt }} -i --create-namespace helm-chart -f helm-chart/values.yaml -f helm-chart/cvat.values.yaml -f helm-chart/test.values.yaml

    - name: Update test config
      run: |
        sed -i -e 's$http://localhost:8080$http://cvat.local:80$g' tests/python/shared/utils/config.py
        find tests/python/shared/assets/ -type f -name '*.json' | xargs sed -i -e 's$http://localhost:8080$http://cvat.local$g'
        echo "$(minikube ip) cvat.local" | sudo tee -a /etc/hosts

    - name: Wait for CVAT to be ready
      run: |
        max_tries=60
        while [[ $(kubectl get pods -l component=server -o 'jsonpath={..status.conditions[?(@.type=="Ready")].status}') != "True" && max_tries -gt 0 ]]; do echo "waiting for CVAT pod" && (( max_tries-- )) && sleep 5; done
        while [[ $(kubectl get pods -l app.kubernetes.io/name=postgresql -o 'jsonpath={..status.conditions[?(@.type=="Ready")].status}') != "True" && max_tries -gt 0 ]]; do echo "waiting for DB pod" && (( max_tries-- )) && sleep 5; done
        while [[ $(curl -s -o /tmp/server_response -w "%{http_code}" cvat.local/api/server/about) != "200" && max_tries -gt 0 ]]; do echo "waiting for CVAT" && (( max_tries-- )) && sleep 5; done
        kubectl get pods
        kubectl logs $(kubectl get pods -l component=server -o jsonpath='{.items[0].metadata.name}')


    - name: Generate SDK
      run: |
        pip3 install --user -r cvat-sdk/gen/requirements.txt
        ./cvat-sdk/gen/generate.sh

    - name: Install test requirements
      run: |
        pip3 install --user cvat-sdk/
        pip3 install --user cvat-cli/
        pip3 install --user -r tests/python/requirements.txt

    - name: REST API and SDK tests
      # We don't have external services in Helm tests, so we ignore corresponding cases
      # They are still tested without Helm
      run: |
        kubectl cp tests/mounted_file_share/images $(kubectl get pods -l component=server -o jsonpath='{.items[0].metadata.name}'):/home/<USER>/share
        pytest --timeout 30 --platform=kube -m "not with_external_services" tests/python --log-cli-level DEBUG

    - name: Creating a log file from "cvat" container logs
      if: failure()
      env:
          LOGS_DIR: "${{ github.workspace }}/rest_api_testing"
      run: |
        mkdir ${LOGS_DIR}
        kubectl logs $(kubectl get pods -l component=server -o 'jsonpath={.items[0].metadata.name}') >${LOGS_DIR}/cvat_server.log
        kubectl logs $(kubectl get pods -l component=worker-import -o 'jsonpath={.items[0].metadata.name}') >${LOGS_DIR}/cvat_worker_import.log
        kubectl logs $(kubectl get pods -l component=worker-export -o 'jsonpath={.items[0].metadata.name}') >${LOGS_DIR}/cvat_worker_export.log
        kubectl logs $(kubectl get pods -l component=worker-webhooks -o 'jsonpath={.items[0].metadata.name}') >${LOGS_DIR}/cvat_worker_webhooks.log
        kubectl logs $(kubectl get pods -l app.kubernetes.io/name=traefik -o 'jsonpath={.items[0].metadata.name}') >${LOGS_DIR}/traefik.log

    - name: Uploading "cvat" container logs as an artifact
      if: failure()
      uses: actions/upload-artifact@v3.1.1
      with:
        name: rest_api_container_logs
        path: "${{ github.workspace }}/rest_api_testing"
