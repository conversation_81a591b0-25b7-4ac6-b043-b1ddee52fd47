name: Pylint
on: pull_request
jobs:
  Linter:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - id: files
        uses: tj-actions/changed-files@v41.0.0
        with:
          files: |
              **/*.py

      - name: Run checks
        env:
          PR_FILES_AM: ${{ steps.files.outputs.added_modified }}
          PR_FILES_RENAMED: ${{ steps.files.outputs.renamed }}
        run: |
          CHANGED_FILES="${{steps.files.outputs.all_changed_files}}"

          if [[ ! -z $CHANGED_FILES ]]; then
            pipx install $(egrep "^pylint==" ./cvat/requirements/development.txt)

            pipx inject pylint \
              $(egrep "^pylint-.+==" ./cvat/requirements/development.txt) \
              $(egrep "^django==" ./cvat/requirements/base.txt)

            echo "Pylint version: "$(pylint --version | head -1)
            echo "The files will be checked: "$(echo $CHANGED_FILES)
            pylint $CHANGED_FILES
          else
            echo "No files with the \"py\" extension found"
          fi
