name: isort
on: pull_request
jobs:
  Linter:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - id: files
        uses: tj-actions/changed-files@v41.0.0
        with:
          files: |
              cvat-sdk/**/*.py
              cvat-cli/**/*.py
              tests/python/**/*.py
              cvat/apps/quality_control/**/*.py
              cvat/apps/analytics_report/**/*.py
          dir_names: true

      - name: Run checks
        run: |
          # If different modules use different isort configs,
          # we need to run isort for each python component group separately.
          # Otherwise, they all will use the same config.

          UPDATED_DIRS="${{steps.files.outputs.all_changed_files}}"

          if [[ ! -z $UPDATED_DIRS ]]; then
            pipx install $(egrep "isort.*" ./cvat-cli/requirements/development.txt)

            echo "isort version: $(isort --version-number)"
            echo "The dirs will be checked: $UPDATED_DIRS"
            EXIT_CODE=0
            for DIR in $UPDATED_DIRS; do
              isort --check $DIR || EXIT_CODE=$(($? | $EXIT_CODE)) || true
            done
            exit $EXIT_CODE
          else
            echo "No files with the \"py\" extension found"
          fi
