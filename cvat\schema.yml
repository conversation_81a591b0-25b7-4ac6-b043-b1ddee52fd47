openapi: 3.0.3
info:
  title: CVAT REST API
  version: 2.12.1
  description: REST API for Computer Vision Annotation Tool (CVAT)
  termsOfService: https://www.google.com/policies/terms/
  contact:
    name: CVAT.ai team
    url: https://github.com/cvat-ai/cvat
    email: <EMAIL>
  license:
    name: MIT License
    url: https://en.wikipedia.org/wiki/MIT_License
paths:
  /api/analytics/reports:
    get:
      operationId: analytics_get_reports
      description: Receive analytics report
      summary: Get an analytics report
      parameters:
      - in: query
        name: end_date
        schema:
          type: string
          format: date-time
        description: Specify the end date for filtering report data.
      - in: query
        name: job_id
        schema:
          type: integer
        description: Specify job ID
      - in: query
        name: project_id
        schema:
          type: integer
        description: Specify project ID
      - in: query
        name: start_date
        schema:
          type: string
          format: date-time
        description: Specify a start date for filtering report data.
      - in: query
        name: task_id
        schema:
          type: integer
        description: Specify task ID
      tags:
      - analytics
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/AnalyticsReport'
          description: ''
        '404':
          description: Not found
    post:
      operationId: analytics_create_report
      summary: Create an analytics report
      parameters:
      - in: query
        name: rq_id
        schema:
          type: string
        description: |
          The report creation request id. Can be specified to check the report
          creation status.
      tags:
      - analytics
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnalyticsReportCreateRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          description: A analytics report request has been computed
        '202':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RqId'
          description: |
            A analytics report request has been enqueued, the request id is returned.
            The request status can be checked at this endpoint by passing the rq_id
            as the query parameter. If the request id is specified, this response
            means the analytics report request is queued or is being processed.
        '400':
          description: Invalid or failed request, check the response data for details
  /api/assets:
    post:
      operationId: assets_create
      summary: Create an asset
      tags:
      - assets
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/AssetRead'
          description: ''
  /api/assets/{uuid}:
    get:
      operationId: assets_retrieve
      summary: Get an asset
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this asset.
        required: true
      tags:
      - assets
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: Asset file
    delete:
      operationId: assets_destroy
      summary: Delete an asset
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this asset.
        required: true
      tags:
      - assets
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The asset has been deleted
  /api/auth/login:
    post:
      operationId: auth_create_login
      description: |-
        Check the credentials and return the REST Token
        if the credentials are valid and authenticated.
        If email verification is enabled and the user has the unverified email,
        an email with a confirmation link will be sent.
        Calls Django Auth login method to register User ID
        in Django session framework.

        Accept the following POST parameters: username, email, password
        Return the REST Framework Token Object's key.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginSerializerExRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/Token'
          description: ''
  /api/auth/logout:
    post:
      operationId: auth_create_logout
      description: |-
        Calls Django logout method and delete the Token object
        assigned to the current User object.

        Accepts/Returns nothing.
      tags:
      - auth
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RestAuthDetail'
          description: ''
  /api/auth/password/change:
    post:
      operationId: auth_create_password_change
      description: |-
        Calls Django Auth SetPasswordForm save method.

        Accepts the following POST parameters: new_password1, new_password2
        Returns the success/fail message.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordChangeRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RestAuthDetail'
          description: ''
  /api/auth/password/reset:
    post:
      operationId: auth_create_password_reset
      description: |-
        Calls Django Auth PasswordResetForm save method.

        Accepts the following POST parameters: email
        Returns the success/fail message.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetSerializerExRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RestAuthDetail'
          description: ''
  /api/auth/password/reset/confirm:
    post:
      operationId: auth_create_password_reset_confirm
      description: |-
        Password reset e-mail link is confirmed, therefore
        this resets the user's password.

        Accepts the following POST parameters: token, uid,
            new_password1, new_password2
        Returns the success/fail message.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetConfirmRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      - {}
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RestAuthDetail'
          description: ''
  /api/auth/register:
    post:
      operationId: auth_create_register
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterSerializerExRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      - {}
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RegisterSerializerEx'
          description: ''
  /api/auth/rules:
    get:
      operationId: auth_retrieve_rules
      tags:
      - auth
      security:
      - {}
      responses:
        '200':
          description: No response body
  /api/auth/signing:
    post:
      operationId: auth_create_signing
      description: Signed URL contains a token which authenticates a user on the server.Signed
        URL is valid during 30 seconds since signing.
      summary: This method signs URL for access to the server
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SigningRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                type: string
          description: text URL
  /api/cloudstorages:
    get:
      operationId: cloudstorages_list
      summary: List cloud storages
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: credentials_type
        in: query
        description: A simple equality filter for the credentials_type field
        schema:
          type: string
          enum:
          - KEY_SECRET_KEY_PAIR
          - ACCOUNT_NAME_TOKEN_PAIR
          - KEY_FILE_PATH
          - ANONYMOUS_ACCESS
          - CONNECTION_STRING
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['provider_type', 'name', 'resource', 'credentials_type', 'owner', 'description', 'id'].
        schema:
          type: string
      - name: name
        in: query
        description: A simple equality filter for the name field
        schema:
          type: string
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: owner
        in: query
        description: A simple equality filter for the owner field
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: provider_type
        in: query
        description: A simple equality filter for the provider_type field
        schema:
          type: string
          enum:
          - AWS_S3_BUCKET
          - AZURE_CONTAINER
          - GOOGLE_DRIVE
          - GOOGLE_CLOUD_STORAGE
      - name: resource
        in: query
        description: A simple equality filter for the resource field
        schema:
          type: string
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''provider_type'',
          ''name'', ''resource'', ''credentials_type'', ''owner'', ''description'')'
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''provider_type'', ''name'', ''resource'', ''credentials_type'', ''owner'',
          ''description'', ''id'']'
        schema:
          type: string
      tags:
      - cloudstorages
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedCloudStorageReadList'
          description: ''
    post:
      operationId: cloudstorages_create
      summary: Create a cloud storage
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      tags:
      - cloudstorages
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CloudStorageWriteRequest'
            examples:
              CreateAWSS3CloudStorageWithCredentials:
                value:
                  provider_type: AWS_S3_BUCKET
                  resource: somebucket
                  display_name: Bucket
                  credentials_type: KEY_SECRET_KEY_PAIR
                  key: XXX
                  secret_key: XXX
                  specific_attributes: region=eu-central-1
                  description: Some description
                  manifests:
                  - manifest.jsonl
                summary: Create AWS S3 cloud storage with credentials
              CreateAWSS3CloudStorageWithoutCredentials:
                value:
                  provider_type: AWS_S3_BUCKET
                  resource: somebucket
                  display_name: Bucket
                  credentials_type: ANONYMOUS_ACCESS
                  manifests:
                  - manifest.jsonl
                summary: Create AWS S3 cloud storage without credentials
              CreateAzureCloudStorage:
                value:
                  provider_type: AZURE_CONTAINER
                  resource: sonecontainer
                  display_name: Container
                  credentials_type: ACCOUNT_NAME_TOKEN_PAIR
                  account_name: someaccount
                  session_token: xxx
                  manifests:
                  - manifest.jsonl
                summary: Create Azure cloud storage
              CreateGCS:
                value:
                  provider_type: GOOGLE_CLOUD_STORAGE
                  resource: somebucket
                  display_name: Bucket
                  credentials_type: KEY_FILE_PATH
                  key_file: file
                  manifests:
                  - manifest.jsonl
                summary: Create GCS
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CloudStorageWriteRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/CloudStorageRead'
          description: ''
  /api/cloudstorages/{id}:
    get:
      operationId: cloudstorages_retrieve
      summary: Get cloud storage details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this cloud storage.
        required: true
      tags:
      - cloudstorages
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/CloudStorageRead'
          description: ''
    patch:
      operationId: cloudstorages_partial_update
      summary: Update a cloud storage
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this cloud storage.
        required: true
      tags:
      - cloudstorages
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCloudStorageWriteRequest'
            examples:
              CreateAWSS3CloudStorageWithCredentials:
                value:
                  provider_type: AWS_S3_BUCKET
                  resource: somebucket
                  display_name: Bucket
                  credentials_type: KEY_SECRET_KEY_PAIR
                  key: XXX
                  secret_key: XXX
                  specific_attributes: region=eu-central-1
                  description: Some description
                  manifests:
                  - manifest.jsonl
                summary: Create AWS S3 cloud storage with credentials
              CreateAWSS3CloudStorageWithoutCredentials:
                value:
                  provider_type: AWS_S3_BUCKET
                  resource: somebucket
                  display_name: Bucket
                  credentials_type: ANONYMOUS_ACCESS
                  manifests:
                  - manifest.jsonl
                summary: Create AWS S3 cloud storage without credentials
              CreateAzureCloudStorage:
                value:
                  provider_type: AZURE_CONTAINER
                  resource: sonecontainer
                  display_name: Container
                  credentials_type: ACCOUNT_NAME_TOKEN_PAIR
                  account_name: someaccount
                  session_token: xxx
                  manifests:
                  - manifest.jsonl
                summary: Create Azure cloud storage
              CreateGCS:
                value:
                  provider_type: GOOGLE_CLOUD_STORAGE
                  resource: somebucket
                  display_name: Bucket
                  credentials_type: KEY_FILE_PATH
                  key_file: file
                  manifests:
                  - manifest.jsonl
                summary: Create GCS
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCloudStorageWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/CloudStorageRead'
          description: ''
    delete:
      operationId: cloudstorages_destroy
      summary: Delete a cloud storage
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this cloud storage.
        required: true
      tags:
      - cloudstorages
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The cloud storage has been removed
  /api/cloudstorages/{id}/actions:
    get:
      operationId: cloudstorages_retrieve_actions
      description: Method return allowed actions for cloud storage. It's required
        for reading/writing
      summary: Get allowed actions for a cloud storage
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this cloud storage.
        required: true
      tags:
      - cloudstorages
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                type: string
          description: Cloud Storage actions (GET | PUT | DELETE)
  /api/cloudstorages/{id}/content-v2:
    get:
      operationId: cloudstorages_retrieve_content_v2
      summary: Get cloud storage content
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this cloud storage.
        required: true
      - in: query
        name: manifest_path
        schema:
          type: string
        description: Path to the manifest file in a cloud storage
      - in: query
        name: next_token
        schema:
          type: string
        description: Used to continue listing files in the bucket
      - in: query
        name: page_size
        schema:
          type: integer
      - in: query
        name: prefix
        schema:
          type: string
        description: Prefix to filter data
      tags:
      - cloudstorages
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/CloudStorageContent'
          description: A manifest content
  /api/cloudstorages/{id}/preview:
    get:
      operationId: cloudstorages_retrieve_preview
      summary: Get a preview image for a cloud storage
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this cloud storage.
        required: true
      tags:
      - cloudstorages
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: Cloud Storage preview
        '400':
          description: Failed to get cloud storage preview
        '404':
          description: Cloud Storage preview not found
  /api/cloudstorages/{id}/status:
    get:
      operationId: cloudstorages_retrieve_status
      summary: Get the status of a cloud storage
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this cloud storage.
        required: true
      tags:
      - cloudstorages
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                type: string
          description: Cloud Storage status (AVAILABLE | NOT_FOUND | FORBIDDEN)
  /api/comments:
    get:
      operationId: comments_list
      summary: List comments
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['owner', 'id', 'issue_id', 'frame_id', 'job_id'].
        schema:
          type: string
      - name: frame_id
        in: query
        description: A simple equality filter for the frame_id field
        schema:
          type: integer
      - name: issue_id
        in: query
        description: A simple equality filter for the issue_id field
        schema:
          type: integer
      - name: job_id
        in: query
        description: A simple equality filter for the job_id field
        schema:
          type: integer
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: owner
        in: query
        description: A simple equality filter for the owner field
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''owner'',)'
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''owner'', ''id'', ''issue_id'', ''frame_id'', ''job_id'']'
        schema:
          type: string
      tags:
      - comments
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedCommentReadList'
          description: ''
    post:
      operationId: comments_create
      summary: Create a comment
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      tags:
      - comments
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommentWriteRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/CommentRead'
          description: ''
  /api/comments/{id}:
    get:
      operationId: comments_retrieve
      summary: Get comment details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this comment.
        required: true
      tags:
      - comments
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/CommentRead'
          description: ''
    patch:
      operationId: comments_partial_update
      summary: Update a comment
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this comment.
        required: true
      tags:
      - comments
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCommentWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/CommentRead'
          description: ''
    delete:
      operationId: comments_destroy
      summary: Delete a comment
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this comment.
        required: true
      tags:
      - comments
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The comment has been deleted
  /api/events:
    get:
      operationId: events_list
      description: The log is returned in the CSV format.
      summary: Get an event log
      parameters:
      - in: query
        name: action
        schema:
          type: string
          enum:
          - download
        description: Used to start downloading process after annotation file had been
          created
      - in: query
        name: filename
        schema:
          type: string
        description: Desired output file name
      - in: query
        name: from
        schema:
          type: string
          format: date-time
        description: Filter events after the datetime. If no 'from' or 'to' parameters
          are passed, the last 30 days will be set.
      - in: query
        name: job_id
        schema:
          type: integer
        description: Filter events by job ID
      - in: query
        name: org_id
        schema:
          type: integer
        description: Filter events by organization ID
      - in: query
        name: project_id
        schema:
          type: integer
        description: Filter events by project ID
      - in: query
        name: query_id
        schema:
          type: string
        description: ID of query request that need to check or download
      - in: query
        name: task_id
        schema:
          type: integer
        description: Filter events by task ID
      - in: query
        name: to
        schema:
          type: string
          format: date-time
        description: Filter events before the datetime. If no 'from' or 'to' parameters
          are passed, the last 30 days will be set.
      - in: query
        name: user_id
        schema:
          type: integer
        description: Filter events by user ID
      tags:
      - events
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: Download of file started
        '201':
          description: CSV log file is ready for downloading
        '202':
          description: Creating a CSV log file has been started
    post:
      operationId: events_create
      description: Sends logs to the Clickhouse if it is connected
      summary: Log client events
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      tags:
      - events
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientEventsRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/ClientEvents'
          description: ''
  /api/guides:
    post:
      operationId: guides_create
      description: The new guide will be bound either to a project or a task, depending
        on parameters.
      summary: Create an annotation guide
      tags:
      - guides
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnnotationGuideWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/AnnotationGuideRead'
          description: ''
  /api/guides/{id}:
    get:
      operationId: guides_retrieve
      summary: Get annotation guide details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this annotation guide.
        required: true
      tags:
      - guides
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/AnnotationGuideRead'
          description: ''
    patch:
      operationId: guides_partial_update
      summary: Update an annotation guide
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this annotation guide.
        required: true
      tags:
      - guides
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAnnotationGuideWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/AnnotationGuideRead'
          description: ''
    delete:
      operationId: guides_destroy
      description: This also deletes all assets attached to the guide.
      summary: Delete an annotation guide
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this annotation guide.
        required: true
      tags:
      - guides
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The annotation guide has been deleted
  /api/invitations:
    get:
      operationId: invitations_list
      summary: List invitations
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['owner', 'user_id', 'accepted'].
        schema:
          type: string
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: owner
        in: query
        description: A simple equality filter for the owner field
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''owner'',)'
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''owner'', ''created_date'']'
        schema:
          type: string
      tags:
      - invitations
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedInvitationReadList'
          description: ''
    post:
      operationId: invitations_create
      summary: Create an invitation
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      tags:
      - invitations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InvitationWriteRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/InvitationRead'
          description: ''
  /api/invitations/{key}:
    get:
      operationId: invitations_retrieve
      summary: Get invitation details
      parameters:
      - in: path
        name: key
        schema:
          type: string
        description: A unique value identifying this invitation.
        required: true
      tags:
      - invitations
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/InvitationRead'
          description: ''
    patch:
      operationId: invitations_partial_update
      summary: Update an invitation
      parameters:
      - in: path
        name: key
        schema:
          type: string
        description: A unique value identifying this invitation.
        required: true
      tags:
      - invitations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedInvitationWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/InvitationRead'
          description: ''
    delete:
      operationId: invitations_destroy
      summary: Delete an invitation
      parameters:
      - in: path
        name: key
        schema:
          type: string
        description: A unique value identifying this invitation.
        required: true
      tags:
      - invitations
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The invitation has been deleted
  /api/invitations/{key}/accept:
    post:
      operationId: invitations_accept
      summary: Accept an invitation
      parameters:
      - in: path
        name: key
        schema:
          type: string
        description: A unique value identifying this invitation.
        required: true
      tags:
      - invitations
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/AcceptInvitationRead'
          description: The invitation is accepted
        '400':
          description: The invitation is expired or already accepted
  /api/invitations/{key}/decline:
    post:
      operationId: invitations_decline
      summary: Decline an invitation
      parameters:
      - in: path
        name: key
        schema:
          type: string
        description: A unique value identifying this invitation.
        required: true
      tags:
      - invitations
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The invitation has been declined
  /api/invitations/{key}/resend:
    post:
      operationId: invitations_resend
      summary: Resend an invitation
      parameters:
      - in: path
        name: key
        schema:
          type: string
        description: A unique value identifying this invitation.
        required: true
      tags:
      - invitations
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: Invitation has been sent
        '400':
          description: The invitation is already accepted
  /api/issues:
    get:
      operationId: issues_list
      summary: List issues
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: assignee
        in: query
        description: A simple equality filter for the assignee field
        schema:
          type: string
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['owner', 'assignee', 'id', 'job_id', 'task_id', 'resolved', 'frame_id'].
        schema:
          type: string
      - name: frame_id
        in: query
        description: A simple equality filter for the frame_id field
        schema:
          type: integer
      - name: job_id
        in: query
        description: A simple equality filter for the job_id field
        schema:
          type: integer
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: owner
        in: query
        description: A simple equality filter for the owner field
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: resolved
        in: query
        description: A simple equality filter for the resolved field
        schema:
          type: boolean
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''owner'', ''assignee'')'
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''owner'', ''assignee'', ''id'', ''job_id'', ''task_id'', ''resolved'',
          ''frame_id'']'
        schema:
          type: string
      - name: task_id
        in: query
        description: A simple equality filter for the task_id field
        schema:
          type: integer
      tags:
      - issues
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedIssueReadList'
          description: ''
    post:
      operationId: issues_create
      summary: Create an issue
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      tags:
      - issues
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IssueWriteRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/IssueRead'
          description: ''
  /api/issues/{id}:
    get:
      operationId: issues_retrieve
      summary: Get issue details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this issue.
        required: true
      tags:
      - issues
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/IssueRead'
          description: ''
    patch:
      operationId: issues_partial_update
      summary: Update an issue
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this issue.
        required: true
      tags:
      - issues
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedIssueWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/IssueRead'
          description: ''
    delete:
      operationId: issues_destroy
      summary: Delete an issue
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this issue.
        required: true
      tags:
      - issues
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The issue has been deleted
  /api/jobs:
    get:
      operationId: jobs_list
      summary: List jobs
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: assignee
        in: query
        description: A simple equality filter for the assignee field
        schema:
          type: string
      - name: dimension
        in: query
        description: A simple equality filter for the dimension field
        schema:
          type: string
          enum:
          - 3d
          - 2d
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['task_name', 'project_name', 'assignee', 'state', 'stage', 'id', 'task_id', 'project_id', 'updated_date', 'dimension', 'type'].
        schema:
          type: string
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: project_id
        in: query
        description: A simple equality filter for the project_id field
        schema:
          type: integer
      - name: project_name
        in: query
        description: A simple equality filter for the project_name field
        schema:
          type: string
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''task_name'', ''project_name'',
          ''assignee'', ''state'', ''stage'')'
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''task_name'', ''project_name'', ''assignee'', ''state'', ''stage'', ''id'',
          ''task_id'', ''project_id'', ''updated_date'', ''dimension'', ''type'']'
        schema:
          type: string
      - name: stage
        in: query
        description: A simple equality filter for the stage field
        schema:
          type: string
          enum:
          - annotation
          - validation
          - acceptance
      - name: state
        in: query
        description: A simple equality filter for the state field
        schema:
          type: string
          enum:
          - new
          - in progress
          - completed
          - rejected
      - name: task_id
        in: query
        description: A simple equality filter for the task_id field
        schema:
          type: integer
      - name: task_name
        in: query
        description: A simple equality filter for the task_name field
        schema:
          type: string
      - name: type
        in: query
        description: A simple equality filter for the type field
        schema:
          type: string
          enum:
          - annotation
          - ground_truth
      tags:
      - jobs
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedJobReadList'
          description: ''
    post:
      operationId: jobs_create
      summary: Create a job
      tags:
      - jobs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JobWriteRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/JobRead'
          description: ''
  /api/jobs/{id}:
    get:
      operationId: jobs_retrieve
      summary: Get job details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      tags:
      - jobs
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/JobRead'
          description: ''
    patch:
      operationId: jobs_partial_update
      summary: Update a job
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      tags:
      - jobs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedJobWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/JobRead'
          description: ''
    delete:
      operationId: jobs_destroy
      description: |
        Related annotations will be deleted as well.

        Please note, that not every job can be removed. Currently,
        it is only available for Ground Truth jobs.
      summary: Delete a job
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      tags:
      - jobs
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The job has been deleted
  /api/jobs/{id}/annotations/:
    get:
      operationId: jobs_retrieve_annotations
      description: |
        If format is specified, a ZIP archive will be returned. Otherwise,
        the annotations will be returned as a JSON document.
      summary: Get job annotations
      parameters:
      - in: query
        name: action
        schema:
          type: string
          enum:
          - download
        description: Used to start downloading process locally after annotation file
          has been created
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Desired output file name
      - in: query
        name: format
        schema:
          type: string
        description: |-
          Desired output format name
          You can get the list of supported formats at:
          /server/annotation/formats
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: Where need to save downloaded annotation
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in the task to export annotation
      tags:
      - jobs
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/AnnotationsRead'
          description: Download of file started
        '201':
          description: Output file is ready for downloading
        '202':
          description: Exporting has been started
        '405':
          description: Format is not available
    post:
      operationId: jobs_create_annotations
      description: |2

        The request POST /api/jobs/id/annotations will initiate the import and will create
        the rq job on the server in which the import will be carried out.
        Please, use the PUT /api/jobs/id/annotations endpoint for checking status of the process.
      summary: Import annotations into a job
      parameters:
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Annotation file name
      - in: query
        name: format
        schema:
          type: string
        description: |-
          Input format name
          You can get the list of supported formats at:
          /server/annotation/formats
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: where to import the annotation from
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in the task to import annotation
      tags:
      - jobs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnnotationFileRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AnnotationFileRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          description: Uploading has finished
        '202':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RqId'
          description: Uploading has been started
        '405':
          description: Format is not available
    put:
      operationId: jobs_update_annotations
      description: |2

        To check the status of an import request:

        After initiating the annotation import, you will receive an rq_id parameter.
        Make sure to include this parameter as a query parameter in your subsequent
        PUT /api/jobs/id/annotations requests to track the status of the import.
      summary: Replace job annotations / Get annotation import status
      parameters:
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Annotation file name
      - in: query
        name: format
        schema:
          type: string
        description: |-
          Input format name
          You can get the list of supported formats at:
          /server/annotation/formats
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: where to import the annotation from
      - in: query
        name: rq_id
        schema:
          type: string
        description: rq id
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in the task to import annotation
      tags:
      - jobs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JobAnnotationsUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/JobAnnotationsUpdateRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          description: Import has finished
        '202':
          description: Import is in progress
        '405':
          description: Format is not available
    patch:
      operationId: jobs_partial_update_annotations
      summary: Update job annotations
      parameters:
      - in: query
        name: action
        schema:
          type: string
          enum:
          - create
          - delete
          - update
        required: true
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      tags:
      - jobs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedLabeledDataRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedLabeledDataRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: Annotations successfully uploaded
    delete:
      operationId: jobs_destroy_annotations
      summary: Delete job annotations
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      tags:
      - jobs
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The annotation has been deleted
  /api/jobs/{id}/data:
    get:
      operationId: jobs_retrieve_data
      summary: Get data of a job
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      - in: query
        name: number
        schema:
          type: integer
        description: A unique number value identifying chunk or frame
      - in: query
        name: quality
        schema:
          type: string
          enum:
          - compressed
          - original
        description: Specifies the quality level of the requested data
      - in: query
        name: type
        schema:
          type: string
          enum:
          - chunk
          - context_image
          - frame
        description: Specifies the type of the requested data
      tags:
      - jobs
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                type: string
                format: binary
          description: Data of a specific type
  /api/jobs/{id}/data/meta:
    get:
      operationId: jobs_retrieve_data_meta
      summary: Get metainformation for media files in a job
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      tags:
      - jobs
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/DataMetaRead'
          description: ''
    patch:
      operationId: jobs_partial_update_data_meta
      summary: Update metainformation for media files in a job
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      tags:
      - tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDataMetaWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/DataMetaRead'
          description: ''
  /api/jobs/{id}/dataset:
    get:
      operationId: jobs_retrieve_dataset
      summary: Export job as a dataset in a specific format
      parameters:
      - in: query
        name: action
        schema:
          type: string
          enum:
          - download
        description: Used to start downloading process locally after annotation file
          has been created
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Desired output file name
      - in: query
        name: format
        schema:
          type: string
        description: |-
          Desired output format name
          You can get the list of supported formats at:
          /server/annotation/formats
        required: true
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: Where need to save downloaded dataset
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in the task to export dataset
      tags:
      - jobs
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                type: string
                format: binary
          description: Download of file started
        '201':
          description: Output file is ready for downloading
        '202':
          description: Exporting has been started
        '405':
          description: Format is not available
  /api/jobs/{id}/preview:
    get:
      operationId: jobs_retrieve_preview
      summary: Get a preview image for a job
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this job.
        required: true
      tags:
      - jobs
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: Job image preview
  /api/labels:
    get:
      operationId: labels_list
      summary: List labels
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - name: color
        in: query
        description: A simple equality filter for the color field
        schema:
          type: string
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['name', 'parent', 'id', 'type', 'color', 'parent_id'].
        schema:
          type: string
      - in: query
        name: job_id
        schema:
          type: integer
        description: A simple equality filter for job id
      - name: name
        in: query
        description: A simple equality filter for the name field
        schema:
          type: string
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: parent
        in: query
        description: A simple equality filter for the parent field
        schema:
          type: string
      - name: parent_id
        in: query
        description: A simple equality filter for the parent_id field
        schema:
          type: integer
      - in: query
        name: project_id
        schema:
          type: integer
        description: A simple equality filter for project id
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''name'', ''parent'')'
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''name'', ''parent'', ''id'', ''type'', ''color'', ''parent_id'']'
        schema:
          type: string
      - in: query
        name: task_id
        schema:
          type: integer
        description: A simple equality filter for task id
      - name: type
        in: query
        description: A simple equality filter for the type field
        schema:
          type: string
          enum:
          - bbox
          - ellipse
          - polygon
          - polyline
          - points
          - cuboid
          - cuboid_3d
          - skeleton
          - tag
          - any
      tags:
      - labels
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedLabelList'
          description: ''
  /api/labels/{id}:
    get:
      operationId: labels_retrieve
      summary: Get label details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this label.
        required: true
      tags:
      - labels
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/Label'
          description: ''
    patch:
      operationId: labels_partial_update
      description: To modify a sublabel, please use the PATCH method of the parent
        label.
      summary: Update a label
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this label.
        required: true
      tags:
      - labels
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedLabelRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/Label'
          description: ''
    delete:
      operationId: labels_destroy
      description: To delete a sublabel, please use the PATCH method of the parent
        label.
      summary: Delete a label
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this label.
        required: true
      tags:
      - labels
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The label has been deleted
  /api/lambda/functions:
    get:
      operationId: lambda_list_functions
      summary: Method returns a list of functions
      tags:
      - lambda
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: No response body
  /api/lambda/functions/{func_id}:
    get:
      operationId: lambda_retrieve_functions
      summary: Method returns the information about the function
      parameters:
      - in: path
        name: func_id
        schema:
          type: string
          pattern: ^[a-zA-Z0-9_.-]+$
        required: true
      tags:
      - lambda
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                type: object
          description: Information about the function
    post:
      operationId: lambda_create_functions
      description: |
        Allows to execute a function for immediate computation.

        Intended for short-lived executions, useful for interactive calls.

        When executed for interactive annotation, the job id must be specified
        in the 'job' input field. The task id is not required in this case,
        but if it is specified, it must match the job task id.
      parameters:
      - in: path
        name: func_id
        schema:
          type: string
          pattern: ^[a-zA-Z0-9_.-]+$
        required: true
      tags:
      - lambda
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnlineFunctionCallRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: Returns function invocation results
  /api/lambda/requests:
    get:
      operationId: lambda_list_requests
      summary: Method returns a list of requests
      tags:
      - lambda
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FunctionCall'
          description: ''
    post:
      operationId: lambda_create_requests
      summary: Method calls the function
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      tags:
      - lambda
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FunctionCallRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/FunctionCall'
          description: ''
  /api/lambda/requests/{id}:
    get:
      operationId: lambda_retrieve_requests
      summary: Method returns the status of the request
      parameters:
      - in: path
        name: id
        schema:
          type: string
        description: Request id
        required: true
      tags:
      - lambda
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/FunctionCall'
          description: ''
    delete:
      operationId: lambda_delete_requests
      summary: Method cancels the request
      parameters:
      - in: path
        name: id
        schema:
          type: string
        description: Request id
        required: true
      tags:
      - lambda
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: No response body
  /api/memberships:
    get:
      operationId: memberships_list
      summary: List memberships
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['user', 'role', 'id'].
        schema:
          type: string
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: role
        in: query
        description: A simple equality filter for the role field
        schema:
          type: string
          enum:
          - worker
          - supervisor
          - maintainer
          - owner
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''user'', ''role'')'
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''user'', ''role'', ''id'']'
        schema:
          type: string
      - name: user
        in: query
        description: A simple equality filter for the user field
        schema:
          type: string
      tags:
      - memberships
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedMembershipReadList'
          description: ''
  /api/memberships/{id}:
    get:
      operationId: memberships_retrieve
      summary: Get membership details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this membership.
        required: true
      tags:
      - memberships
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/MembershipRead'
          description: ''
    patch:
      operationId: memberships_partial_update
      summary: Update a membership
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this membership.
        required: true
      tags:
      - memberships
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedMembershipWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/MembershipRead'
          description: ''
    delete:
      operationId: memberships_destroy
      summary: Delete a membership
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this membership.
        required: true
      tags:
      - memberships
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The membership has been deleted
  /api/organizations:
    get:
      operationId: organizations_list
      summary: List organizations
      parameters:
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['name', 'owner', 'slug', 'id'].
        schema:
          type: string
      - name: name
        in: query
        description: A simple equality filter for the name field
        schema:
          type: string
      - name: owner
        in: query
        description: A simple equality filter for the owner field
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''name'', ''owner'',
          ''slug'')'
        schema:
          type: string
      - name: slug
        in: query
        description: A simple equality filter for the slug field
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''name'', ''owner'', ''slug'', ''id'']'
        schema:
          type: string
      tags:
      - organizations
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedOrganizationReadList'
          description: ''
    post:
      operationId: organizations_create
      summary: Create an organization
      tags:
      - organizations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrganizationWriteRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/OrganizationRead'
          description: ''
  /api/organizations/{id}:
    get:
      operationId: organizations_retrieve
      summary: Get organization details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this organization.
        required: true
      tags:
      - organizations
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/OrganizationRead'
          description: ''
    patch:
      operationId: organizations_partial_update
      summary: Update an organization
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this organization.
        required: true
      tags:
      - organizations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedOrganizationWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/OrganizationRead'
          description: ''
    delete:
      operationId: organizations_destroy
      summary: Delete an organization
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this organization.
        required: true
      tags:
      - organizations
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The organization has been deleted
  /api/projects:
    get:
      operationId: projects_list
      summary: List projects
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: assignee
        in: query
        description: A simple equality filter for the assignee field
        schema:
          type: string
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['name', 'owner', 'assignee', 'status', 'id', 'updated_date'].
        schema:
          type: string
      - name: name
        in: query
        description: A simple equality filter for the name field
        schema:
          type: string
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: owner
        in: query
        description: A simple equality filter for the owner field
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''name'', ''owner'',
          ''assignee'', ''status'')'
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''name'', ''owner'', ''assignee'', ''status'', ''id'', ''updated_date'']'
        schema:
          type: string
      - name: status
        in: query
        description: A simple equality filter for the status field
        schema:
          type: string
          enum:
          - annotation
          - validation
          - completed
      tags:
      - projects
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedProjectReadList'
          description: ''
    post:
      operationId: projects_create
      summary: Create a project
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      tags:
      - projects
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectWriteRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/ProjectRead'
          description: ''
  /api/projects/{id}:
    get:
      operationId: projects_retrieve
      summary: Get project details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this project.
        required: true
      tags:
      - projects
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/ProjectRead'
          description: ''
    patch:
      operationId: projects_partial_update
      summary: Update a project
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this project.
        required: true
      tags:
      - projects
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedProjectWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/ProjectRead'
          description: ''
    delete:
      operationId: projects_destroy
      summary: Delete a project
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this project.
        required: true
      tags:
      - projects
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The project has been deleted
  /api/projects/{id}/annotations:
    get:
      operationId: projects_retrieve_annotations
      summary: Get project annotations
      parameters:
      - in: query
        name: action
        schema:
          type: string
          enum:
          - download
        description: Used to start downloading process locally after annotation file
          has been created
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Desired output file name
      - in: query
        name: format
        schema:
          type: string
        description: |-
          Desired output format name
          You can get the list of supported formats at:
          /server/annotation/formats
        required: true
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this project.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: Where need to save downloaded dataset
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in project to export annotation
      tags:
      - projects
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/AnnotationsRead'
          description: Download of file started
        '201':
          description: Annotations file is ready to download
        '202':
          description: Dump of annotations has been started
        '401':
          description: Format is not specified
        '405':
          description: Format is not available
  /api/projects/{id}/backup:
    get:
      operationId: projects_retrieve_backup
      summary: Back up a project
      parameters:
      - in: query
        name: action
        schema:
          type: string
          enum:
          - download
        description: Used to start downloading process after backup file had been
          created
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Backup file name
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this project.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: Where need to save downloaded backup
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in project to export backup
      tags:
      - projects
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: Download of file started
        '201':
          description: Output backup file is ready for downloading
        '202':
          description: Creating a backup file has been started
  /api/projects/{id}/dataset/:
    get:
      operationId: projects_retrieve_dataset
      description: |2

        To check the status of the process of importing a project dataset from a file:

        After initiating the dataset upload, you will receive an rq_id parameter.
        Make sure to include this parameter as a query parameter in your subsequent
        GET /api/projects/id/dataset requests to track the status of the dataset import.
        Also you should specify action parameter: action=import_status.
      summary: Export a project as a dataset / Check dataset import status
      parameters:
      - in: query
        name: action
        schema:
          type: string
          enum:
          - download
          - import_status
        description: Used to start downloading process locally after annotation file
          has been created
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Desired output file name
      - in: query
        name: format
        schema:
          type: string
        description: |-
          Desired output format name
          You can get the list of supported formats at:
          /server/annotation/formats
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this project.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: Where need to save downloaded dataset
      - in: query
        name: rq_id
        schema:
          type: string
        description: rq id
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in project to import dataset
      tags:
      - projects
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                type: string
                format: binary
          description: Download of file started
        '201':
          description: Output file is ready for downloading
        '202':
          description: Exporting has been started
        '405':
          description: Format is not available
    post:
      operationId: projects_create_dataset
      description: |2

        The request POST /api/projects/id/dataset will initiate file upload and will create
        the rq job on the server in which the process of dataset import from a file
        will be carried out. Please, use the GET /api/projects/id/dataset endpoint for checking status of the process.
      summary: Import a dataset into a project
      parameters:
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Dataset file name
      - in: query
        name: format
        schema:
          type: string
        description: |-
          Desired dataset format name
          You can get the list of supported formats at:
          /server/annotation/formats
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this project.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: Where to import the dataset from
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in the project to import
          annotations
      tags:
      - projects
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DatasetWriteRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DatasetWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '202':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RqId'
          description: Importing has been started
        '400':
          description: Failed to import dataset
        '405':
          description: Format is not available
  /api/projects/{id}/preview:
    get:
      operationId: projects_retrieve_preview
      summary: Get a preview image for a project
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this project.
        required: true
      tags:
      - projects
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: Project image preview
        '404':
          description: Project image preview not found
  /api/projects/backup/:
    post:
      operationId: projects_create_backup
      description: |2

        The backup import process is as follows:

        The first request POST /api/projects/backup will initiate file upload and will create
        the rq job on the server in which the process of a project creating from an uploaded backup
        will be carried out.

        After initiating the backup upload, you will receive an rq_id parameter.
        Make sure to include this parameter as a query parameter in your subsequent requests
        to track the status of the project creation.
        Once the project has been successfully created, the server will return the id of the newly created project.
      summary: Recreate a project from a backup
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Backup file name
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
          default: local
        description: Where to import the backup file from
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      - in: query
        name: rq_id
        schema:
          type: string
        description: rq id
      tags:
      - projects
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BackupWriteRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/BackupWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          description: The project has been imported
        '202':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RqId'
          description: Importing a backup file has been started
  /api/quality/conflicts:
    get:
      operationId: quality_list_conflicts
      summary: List annotation conflicts in a quality report
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['id', 'frame', 'type', 'job_id', 'task_id', 'severity'].
        schema:
          type: string
      - name: frame
        in: query
        description: A simple equality filter for the frame field
        schema:
          type: integer
      - name: job_id
        in: query
        description: A simple equality filter for the job_id field
        schema:
          type: integer
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - in: query
        name: report_id
        schema:
          type: integer
        description: A simple equality filter for report id
      - name: severity
        in: query
        description: A simple equality filter for the severity field
        schema:
          type: string
          enum:
          - warning
          - error
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''id'', ''frame'', ''type'', ''job_id'', ''task_id'', ''severity'']'
        schema:
          type: string
      - name: task_id
        in: query
        description: A simple equality filter for the task_id field
        schema:
          type: integer
      - name: type
        in: query
        description: A simple equality filter for the type field
        schema:
          type: string
          enum:
          - missing_annotation
          - extra_annotation
          - mismatching_label
          - low_overlap
          - mismatching_direction
          - mismatching_attributes
          - mismatching_groups
          - covered_annotation
      tags:
      - quality
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedAnnotationConflictList'
          description: ''
  /api/quality/reports:
    get:
      operationId: quality_list_reports
      summary: List quality reports
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['id', 'job_id', 'created_date', 'gt_last_updated', 'target_last_updated', 'parent_id'].
        schema:
          type: string
      - name: job_id
        in: query
        description: A simple equality filter for the job_id field
        schema:
          type: integer
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: parent_id
        in: query
        description: A simple equality filter for the parent_id field
        schema:
          type: integer
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''id'', ''job_id'', ''created_date'', ''gt_last_updated'', ''target_last_updated'',
          ''parent_id'']'
        schema:
          type: string
      - in: query
        name: target
        schema:
          type: string
        description: A simple equality filter for target
      - in: query
        name: task_id
        schema:
          type: integer
        description: A simple equality filter for task id
      tags:
      - quality
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedQualityReportList'
          description: ''
    post:
      operationId: quality_create_report
      summary: Create a quality report
      parameters:
      - in: query
        name: rq_id
        schema:
          type: string
        description: |
          The report creation request id. Can be specified to check the report
          creation status.
      tags:
      - quality
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QualityReportCreateRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/QualityReport'
          description: ''
        '202':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RqId'
          description: |
            A quality report request has been enqueued, the request id is returned.
            The request status can be checked at this endpoint by passing the rq_id
            as the query parameter. If the request id is specified, this response
            means the quality report request is queued or is being processed.
        '400':
          description: Invalid or failed request, check the response data for details
  /api/quality/reports/{id}:
    get:
      operationId: quality_retrieve_report
      summary: Get quality report details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this quality report.
        required: true
      tags:
      - quality
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/QualityReport'
          description: ''
  /api/quality/reports/{id}/data:
    get:
      operationId: quality_retrieve_report_data
      summary: Get quality report contents
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this quality report.
        required: true
      tags:
      - quality
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                type: object
          description: ''
  /api/quality/settings:
    get:
      operationId: quality_list_settings
      summary: List quality settings instances
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['id', 'task_id'].
        schema:
          type: string
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''id'']'
        schema:
          type: string
      - name: task_id
        in: query
        description: A simple equality filter for the task_id field
        schema:
          type: integer
      tags:
      - quality
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedQualitySettingsList'
          description: ''
  /api/quality/settings/{id}:
    get:
      operationId: quality_retrieve_settings
      summary: Get quality settings instance details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: An id of a quality settings instance
        required: true
      tags:
      - quality
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/QualitySettings'
          description: ''
    patch:
      operationId: quality_partial_update_settings
      summary: Update a quality settings instance
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: An id of a quality settings instance
        required: true
      tags:
      - quality
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedQualitySettingsRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/QualitySettings'
          description: ''
  /api/schema/:
    get:
      operationId: schema_retrieve
      description: |-
        OpenApi3 schema for this API. Format can be selected via content negotiation.

        - YAML: application/vnd.oai.openapi
        - JSON: application/vnd.oai.openapi+json
      parameters:
      - in: query
        name: lang
        schema:
          type: string
          enum:
          - af
          - ar
          - ar-dz
          - ast
          - az
          - be
          - bg
          - bn
          - br
          - bs
          - ca
          - ckb
          - cs
          - cy
          - da
          - de
          - dsb
          - el
          - en
          - en-au
          - en-gb
          - eo
          - es
          - es-ar
          - es-co
          - es-mx
          - es-ni
          - es-ve
          - et
          - eu
          - fa
          - fi
          - fr
          - fy
          - ga
          - gd
          - gl
          - he
          - hi
          - hr
          - hsb
          - hu
          - hy
          - ia
          - id
          - ig
          - io
          - is
          - it
          - ja
          - ka
          - kab
          - kk
          - km
          - kn
          - ko
          - ky
          - lb
          - lt
          - lv
          - mk
          - ml
          - mn
          - mr
          - ms
          - my
          - nb
          - ne
          - nl
          - nn
          - os
          - pa
          - pl
          - pt
          - pt-br
          - ro
          - ru
          - sk
          - sl
          - sq
          - sr
          - sr-latn
          - sv
          - sw
          - ta
          - te
          - tg
          - th
          - tk
          - tr
          - tt
          - udm
          - uk
          - ur
          - uz
          - vi
          - zh-hans
          - zh-hant
      - in: query
        name: scheme
        schema:
          type: string
          enum:
          - json
          - yaml
      tags:
      - schema
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.oai.openapi:
              schema:
                type: object
            application/yaml:
              schema:
                type: object
            application/vnd.oai.openapi+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
          description: ''
  /api/server/about:
    get:
      operationId: server_retrieve_about
      summary: Get basic CVAT information
      tags:
      - server
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/About'
          description: ''
  /api/server/annotation/formats:
    get:
      operationId: server_retrieve_annotation_formats
      summary: Get supported annotation formats
      tags:
      - server
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/DatasetFormats'
          description: ''
  /api/server/plugins:
    get:
      operationId: server_retrieve_plugins
      summary: Get enabled plugins
      tags:
      - server
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/Plugins'
          description: ''
  /api/server/share:
    get:
      operationId: server_list_share
      summary: List files/directories in the mounted share
      parameters:
      - in: query
        name: directory
        schema:
          type: string
        description: Directory to browse
      - in: query
        name: search
        schema:
          type: string
        description: Search for specific files
      tags:
      - server
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FileInfo'
          description: ''
  /api/tasks:
    get:
      operationId: tasks_list
      summary: List tasks
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: assignee
        in: query
        description: A simple equality filter for the assignee field
        schema:
          type: string
      - name: dimension
        in: query
        description: A simple equality filter for the dimension field
        schema:
          type: string
          enum:
          - 3d
          - 2d
      - name: filter
        required: false
        in: query
        description: |2+

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['project_name', 'name', 'owner', 'status', 'assignee', 'subset', 'mode', 'dimension', 'tracker_link', 'id', 'project_id', 'updated_date'].

          There are few examples for complex filtering tasks:

              - Get all tasks from 1,2,3 projects - { "and" : [{ "in" : [{ "var" : "project_id" }, [1, 2, 3]]}]}

              - Get all completed tasks from 1 project - { "and": [{ "==": [{ "var" : "status" }, "completed"]}, { "==" : [{ "var" : "project_id"}, 1]}]}

        schema:
          type: string
      - name: mode
        in: query
        description: A simple equality filter for the mode field
        schema:
          type: string
      - name: name
        in: query
        description: A simple equality filter for the name field
        schema:
          type: string
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: owner
        in: query
        description: A simple equality filter for the owner field
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: project_id
        in: query
        description: A simple equality filter for the project_id field
        schema:
          type: integer
      - name: project_name
        in: query
        description: A simple equality filter for the project_name field
        schema:
          type: string
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''project_name'', ''name'',
          ''owner'', ''status'', ''assignee'', ''subset'', ''mode'', ''dimension'',
          ''tracker_link'')'
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''project_name'', ''name'', ''owner'', ''status'', ''assignee'', ''subset'',
          ''mode'', ''dimension'', ''tracker_link'', ''id'', ''project_id'', ''updated_date'']'
        schema:
          type: string
      - name: status
        in: query
        description: A simple equality filter for the status field
        schema:
          type: string
          enum:
          - annotation
          - validation
          - completed
      - name: subset
        in: query
        description: A simple equality filter for the subset field
        schema:
          type: string
      - name: tracker_link
        in: query
        description: A simple equality filter for the tracker_link field
        schema:
          type: string
      tags:
      - tasks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedTaskReadList'
          description: ''
    post:
      operationId: tasks_create
      description: |
        The new task will not have any attached images or videos.
        To attach them, use the /api/tasks/<id>/data endpoint.
      summary: Create a task
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      tags:
      - tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskWriteRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/TaskRead'
          description: ''
  /api/tasks/{id}:
    get:
      operationId: tasks_retrieve
      summary: Get task details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      tags:
      - tasks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/TaskRead'
          description: ''
    patch:
      operationId: tasks_partial_update
      summary: Update a task
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      tags:
      - tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedTaskWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/TaskRead'
          description: ''
    delete:
      operationId: tasks_destroy
      description: All attached jobs, annotations and data will be deleted as well.
      summary: Delete a task
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      tags:
      - tasks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The task has been deleted
  /api/tasks/{id}/annotations/:
    get:
      operationId: tasks_retrieve_annotations
      summary: Get task annotations
      parameters:
      - in: query
        name: action
        schema:
          type: string
          enum:
          - download
        description: Used to start downloading process locally after annotation file
          has been created
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Desired output file name
      - in: query
        name: format
        schema:
          type: string
        description: |-
          Desired output format name
          You can get the list of supported formats at:
          /server/annotation/formats
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: Where need to save downloaded dataset
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in the task to export annotation
      tags:
      - tasks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/AnnotationsRead'
          description: Download of file started
        '201':
          description: Annotations file is ready to download
        '202':
          description: Dump of annotations has been started
        '400':
          description: Exporting without data is not allowed
        '405':
          description: Format is not available
    post:
      operationId: tasks_create_annotations
      description: |2

        The request POST /api/tasks/id/annotations will initiate the import and will create
        the rq job on the server in which the import will be carried out.
        Please, use the PUT /api/tasks/id/annotations endpoint for checking status of the process.
      summary: Import annotations into a task
      parameters:
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Annotation file name
      - in: query
        name: format
        schema:
          type: string
        description: |-
          Input format name
          You can get the list of supported formats at:
          /server/annotation/formats
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: where to import the annotation from
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in task to import annotations
      tags:
      - tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskAnnotationsWriteRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TaskAnnotationsWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          description: Uploading has finished
        '202':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RqId'
          description: Uploading has been started
        '405':
          description: Format is not available
    put:
      operationId: tasks_update_annotations
      description: |2

        To check the status of an import request:

        After initiating the annotation import, you will receive an rq_id parameter.
        Make sure to include this parameter as a query parameter in your subsequent
        PUT /api/tasks/id/annotations requests to track the status of the import.
      summary: Replace task annotations / Get annotation import status
      parameters:
      - in: query
        name: format
        schema:
          type: string
        description: |-
          Input format name
          You can get the list of supported formats at:
          /server/annotation/formats
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      - in: query
        name: rq_id
        schema:
          type: string
        description: rq id
      tags:
      - tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskAnnotationsUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TaskAnnotationsUpdateRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          description: Import has finished
        '202':
          description: Import is in progress
        '405':
          description: Format is not available
    patch:
      operationId: tasks_partial_update_annotations
      summary: Update task annotations
      parameters:
      - in: query
        name: action
        schema:
          type: string
          enum:
          - create
          - delete
          - update
        required: true
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      tags:
      - tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedLabeledDataRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedLabeledDataRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/LabeledData'
          description: ''
    delete:
      operationId: tasks_destroy_annotations
      summary: Delete task annotations
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      tags:
      - tasks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The annotation has been deleted
  /api/tasks/{id}/backup:
    get:
      operationId: tasks_retrieve_backup
      summary: Back up a task
      parameters:
      - in: query
        name: action
        schema:
          type: string
          enum:
          - download
        description: Used to start downloading process after backup file had been
          created
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Backup file name
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: Where need to save downloaded backup
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in the task to export backup
      tags:
      - tasks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: Download of file started
        '201':
          description: Output backup file is ready for downloading
        '202':
          description: Creating a backup file has been started
        '400':
          description: Backup of a task without data is not allowed
  /api/tasks/{id}/data/:
    get:
      operationId: tasks_retrieve_data
      summary: Get data of a task
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      - in: query
        name: number
        schema:
          type: integer
        description: A unique number value identifying chunk or frame
      - in: query
        name: quality
        schema:
          type: string
          enum:
          - compressed
          - original
        description: Specifies the quality level of the requested data
      - in: query
        name: type
        schema:
          type: string
          enum:
          - chunk
          - context_image
          - frame
        description: Specifies the type of the requested data
      tags:
      - tasks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: Data of a specific type
    post:
      operationId: tasks_create_data
      description: |
        Allows to upload data (images, video, etc.) to a task.
        Supports the TUS open file uploading protocol (https://tus.io/).

        Supports the following protocols:

        1. A single Data request

        and

        2.1. An Upload-Start request
        2.2.a. Regular TUS protocol requests (Upload-Length + Chunks)
        2.2.b. Upload-Multiple requests
        2.3. An Upload-Finish request

        Requests:
        - Data - POST, no extra headers or 'Upload-Start' + 'Upload-Finish' headers.
          Contains data in the body.
        - Upload-Start - POST, has an 'Upload-Start' header. No body is expected.
        - Upload-Length - POST, has an 'Upload-Length' header (see the TUS specification)
        - Chunk - HEAD/PATCH (see the TUS specification). Sent to /data/<file id> endpoints.
        - Upload-Finish - POST, has an 'Upload-Finish' header. Can contain data in the body.
        - Upload-Multiple - POST, has an 'Upload-Multiple' header. Contains data in the body.

        The 'Upload-Finish' request allows to specify the uploaded files should be ordered.
        This may be needed if the files can be sent unordered. To state that the input files
        are sent ordered, pass an empty list of files in the 'upload_file_order' field.
        If the files are sent unordered, the ordered file list is expected
        in the 'upload_file_order' field. It must be a list of string file paths,
        relative to the dataset root.

        Example:
        files = [
            "cats/cat_1.jpg",
            "dogs/dog2.jpg",
            "image_3.png",
            ...
        ]

        Independently of the file declaration field used
        ('client_files', 'server_files', etc.), when the 'predefined'
        sorting method is selected, the uploaded files will be ordered according
        to the '.jsonl' manifest file, if it is found in the list of files.
        For archives (e.g. '.zip'), a manifest file ('*.jsonl') is required when using
        the 'predefined' file ordering. Such file must be provided next to the archive
        in the list of files. Read more about manifest files here:
        https://docs.cvat.ai/docs/manual/advanced/dataset_manifest/

        After all data is sent, the operation status can be retrieved via
        the /status endpoint.

        Once data is attached to a task, it cannot be detached or replaced.
      summary: Attach data to a task
      parameters:
      - in: header
        name: Upload-Finish
        schema:
          type: boolean
        description: Finishes data upload. Can be combined with Upload-Start header
          to create task data with one request
      - in: header
        name: Upload-Multiple
        schema:
          type: boolean
        description: Indicates that data with this request are single or multiple
          files that should be attached to a task
      - in: header
        name: Upload-Start
        schema:
          type: boolean
        description: Initializes data upload. Optionally, can include upload metadata
          in the request body.
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      tags:
      - tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DataRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '202':
          description: No response body
  /api/tasks/{id}/data/meta:
    get:
      operationId: tasks_retrieve_data_meta
      summary: Get metainformation for media files in a task
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      tags:
      - tasks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/DataMetaRead'
          description: ''
    patch:
      operationId: tasks_partial_update_data_meta
      summary: Update metainformation for media files in a task
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      tags:
      - tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDataMetaWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/DataMetaRead'
          description: ''
  /api/tasks/{id}/dataset:
    get:
      operationId: tasks_retrieve_dataset
      summary: Export task as a dataset in a specific format
      parameters:
      - in: query
        name: action
        schema:
          type: string
          enum:
          - download
        description: Used to start downloading process locally after annotation file
          has been created
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Desired output file name
      - in: query
        name: format
        schema:
          type: string
        description: |-
          Desired output format name
          You can get the list of supported formats at:
          /server/annotation/formats
        required: true
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
        description: Where need to save downloaded dataset
      - in: query
        name: use_default_location
        schema:
          type: boolean
          default: true
        description: Use the location that was configured in task to export annotations
      tags:
      - tasks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                type: string
                format: binary
          description: Download of file started
        '201':
          description: Output file is ready for downloading
        '202':
          description: Exporting has been started
        '400':
          description: Exporting without data is not allowed
        '405':
          description: Format is not available
  /api/tasks/{id}/preview:
    get:
      operationId: tasks_retrieve_preview
      summary: Get a preview image for a task
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      tags:
      - tasks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: Task image preview
        '404':
          description: Task image preview not found
  /api/tasks/{id}/status:
    get:
      operationId: tasks_retrieve_status
      summary: Get the creation status of a task
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this task.
        required: true
      tags:
      - tasks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RqStatus'
          description: ''
  /api/tasks/backup/:
    post:
      operationId: tasks_create_backup
      description: |2

        The backup import process is as follows:

        The first request POST /api/tasks/backup will initiate file upload and will create
        the rq job on the server in which the process of a task creating from an uploaded backup
        will be carried out.

        After initiating the backup upload, you will receive an rq_id parameter.
        Make sure to include this parameter as a query parameter in your subsequent requests
        to track the status of the task creation.
        Once the task has been successfully created, the server will return the id of the newly created task.
      summary: Recreate a task from a backup
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: cloud_storage_id
        schema:
          type: integer
        description: Storage id
      - in: query
        name: filename
        schema:
          type: string
        description: Backup file name
      - in: query
        name: location
        schema:
          type: string
          enum:
          - cloud_storage
          - local
          default: local
        description: Where to import the backup file from
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      - in: query
        name: rq_id
        schema:
          type: string
        description: rq id
      tags:
      - tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskFileRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TaskFileRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          description: The task has been imported
        '202':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/RqId'
          description: Importing a backup file has been started
  /api/users:
    get:
      operationId: users_list
      summary: List users
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['username', 'first_name', 'last_name', 'id', 'is_active'].
        schema:
          type: string
      - name: first_name
        in: query
        description: A simple equality filter for the first_name field
        schema:
          type: string
      - name: is_active
        in: query
        description: A simple equality filter for the is_active field
        schema:
          type: boolean
      - name: last_name
        in: query
        description: A simple equality filter for the last_name field
        schema:
          type: string
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''username'', ''first_name'',
          ''last_name'')'
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''username'', ''first_name'', ''last_name'', ''id'', ''is_active'']'
        schema:
          type: string
      - name: username
        in: query
        description: A simple equality filter for the username field
        schema:
          type: string
      tags:
      - users
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedMetaUserList'
          description: ''
  /api/users/{id}:
    get:
      operationId: users_retrieve
      summary: Get user details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this user.
        required: true
      tags:
      - users
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/MetaUser'
          description: ''
    patch:
      operationId: users_partial_update
      summary: Update a user
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this user.
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/MetaUser'
          description: ''
    delete:
      operationId: users_destroy
      summary: Delete a user
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this user.
        required: true
      tags:
      - users
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The user has been deleted
  /api/users/self:
    get:
      operationId: users_retrieve_self
      description: Method returns an instance of a user who is currently authenticated
      summary: Get details of the current user
      tags:
      - users
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/MetaUser'
          description: ''
  /api/webhooks:
    get:
      operationId: webhooks_list
      summary: List webhooks
      parameters:
      - name: X-Organization
        in: header
        description: Organization unique slug
        schema:
          type: string
      - name: filter
        required: false
        in: query
        description: |2-

          JSON Logic filter. This filter can be used to perform complex filtering by grouping rules.

          For example, using such a filter you can get all resources created by you:

              - {"and":[{"==":[{"var":"owner"},"<user>"]}]}

          Details about the syntax used can be found at the link: https://jsonlogic.com/

           Available filter_fields: ['target_url', 'owner', 'type', 'description', 'id', 'project_id', 'updated_date'].
        schema:
          type: string
      - name: org
        in: query
        description: Organization unique slug
        schema:
          type: string
      - name: org_id
        in: query
        description: Organization identifier
        schema:
          type: integer
      - name: owner
        in: query
        description: A simple equality filter for the owner field
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: project_id
        in: query
        description: A simple equality filter for the project_id field
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: 'A search term. Available search_fields: (''target_url'', ''owner'',
          ''type'', ''description'')'
        schema:
          type: string
      - name: sort
        required: false
        in: query
        description: 'Which field to use when ordering the results. Available ordering_fields:
          [''target_url'', ''owner'', ''type'', ''description'', ''id'', ''project_id'',
          ''updated_date'']'
        schema:
          type: string
      - name: target_url
        in: query
        description: A simple equality filter for the target_url field
        schema:
          type: string
      - name: type
        in: query
        description: A simple equality filter for the type field
        schema:
          type: string
          enum:
          - organization
          - project
      tags:
      - webhooks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedWebhookReadList'
          description: ''
    post:
      operationId: webhooks_create
      summary: Create a webhook
      parameters:
      - in: header
        name: X-Organization
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org
        schema:
          type: string
        description: Organization unique slug
      - in: query
        name: org_id
        schema:
          type: integer
        description: Organization identifier
      tags:
      - webhooks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookWriteRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '201':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/WebhookRead'
          description: ''
  /api/webhooks/{id}:
    get:
      operationId: webhooks_retrieve
      summary: Get webhook details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this webhook.
        required: true
      tags:
      - webhooks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/WebhookRead'
          description: ''
    put:
      operationId: webhooks_update
      summary: Replace a webhook
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this webhook.
        required: true
      tags:
      - webhooks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookWriteRequest'
        required: true
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/WebhookRead'
          description: ''
    patch:
      operationId: webhooks_partial_update
      summary: Update a webhook
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this webhook.
        required: true
      tags:
      - webhooks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedWebhookWriteRequest'
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/WebhookRead'
          description: ''
    delete:
      operationId: webhooks_destroy
      summary: Delete a webhook
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this webhook.
        required: true
      tags:
      - webhooks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '204':
          description: The webhook has been deleted
  /api/webhooks/{id}/deliveries:
    get:
      operationId: webhooks_list_deliveries
      summary: List deliveries for a webhook
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this webhook.
        required: true
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      tags:
      - webhooks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/PaginatedWebhookDeliveryReadList'
          description: ''
  /api/webhooks/{id}/deliveries/{delivery_id}:
    get:
      operationId: webhooks_retrieve_deliveries
      summary: Get details of a webhook delivery
      parameters:
      - in: path
        name: delivery_id
        schema:
          type: string
          pattern: ^\d+$
        required: true
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this webhook.
        required: true
      tags:
      - webhooks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/WebhookDeliveryRead'
          description: ''
  /api/webhooks/{id}/deliveries/{delivery_id}/redelivery:
    post:
      operationId: webhooks_create_deliveries_redelivery
      summary: Redeliver a webhook delivery
      parameters:
      - in: path
        name: delivery_id
        schema:
          type: string
          pattern: ^\d+$
        required: true
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this webhook.
        required: true
      tags:
      - webhooks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          description: No response body
  /api/webhooks/{id}/ping:
    post:
      operationId: webhooks_create_ping
      summary: Send a ping webhook
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this webhook.
        required: true
      tags:
      - webhooks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/WebhookDeliveryRead'
          description: ''
  /api/webhooks/events:
    get:
      operationId: webhooks_retrieve_events
      summary: List available webhook events
      parameters:
      - in: query
        name: type
        schema:
          type: string
        description: Type of webhook
      tags:
      - webhooks
      security:
      - sessionAuth: []
        csrfAuth: []
        tokenAuth: []
      - signatureAuth: []
      - basicAuth: []
      responses:
        '200':
          content:
            application/vnd.cvat+json:
              schema:
                $ref: '#/components/schemas/Events'
          description: ''
components:
  schemas:
    About:
      type: object
      properties:
        name:
          type: string
          maxLength: 128
        description:
          type: string
          maxLength: 2048
        version:
          type: string
          maxLength: 64
      required:
      - description
      - name
      - version
    AcceptInvitationRead:
      type: object
      properties:
        organization_slug:
          type: string
      required:
      - organization_slug
    AnalyticsReport:
      type: object
      properties:
        created_date:
          type: string
          format: date-time
        target:
          $ref: '#/components/schemas/AnalyticsReportTargetEnum'
        job_id:
          type: integer
        task_id:
          type: integer
        project_id:
          type: integer
        statistics:
          type: array
          items:
            $ref: '#/components/schemas/Metric'
      required:
      - created_date
      - statistics
      - target
    AnalyticsReportCreateRequest:
      type: object
      properties:
        job_id:
          type: integer
        task_id:
          type: integer
        project_id:
          type: integer
    AnalyticsReportTargetEnum:
      enum:
      - job
      - task
      - project
      type: string
      description: |-
        * `job` - JOB
        * `task` - TASK
        * `project` - PROJECT
    AnnotationConflict:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        frame:
          type: integer
          readOnly: true
        type:
          allOf:
          - $ref: '#/components/schemas/AnnotationConflictTypeEnum'
          readOnly: true
        annotation_ids:
          type: array
          items:
            $ref: '#/components/schemas/AnnotationId'
        report_id:
          type: integer
          readOnly: true
        severity:
          allOf:
          - $ref: '#/components/schemas/SeverityEnum'
          readOnly: true
      required:
      - annotation_ids
    AnnotationConflictTypeEnum:
      enum:
      - missing_annotation
      - extra_annotation
      - mismatching_label
      - low_overlap
      - mismatching_direction
      - mismatching_attributes
      - mismatching_groups
      - covered_annotation
      type: string
      description: |-
        * `missing_annotation` - MISSING_ANNOTATION
        * `extra_annotation` - EXTRA_ANNOTATION
        * `mismatching_label` - MISMATCHING_LABEL
        * `low_overlap` - LOW_OVERLAP
        * `mismatching_direction` - MISMATCHING_DIRECTION
        * `mismatching_attributes` - MISMATCHING_ATTRIBUTES
        * `mismatching_groups` - MISMATCHING_GROUPS
        * `covered_annotation` - COVERED_ANNOTATION
    AnnotationFileRequest:
      type: object
      properties:
        annotation_file:
          type: string
          format: binary
      required:
      - annotation_file
    AnnotationGuideRead:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        task_id:
          type: integer
          nullable: true
          readOnly: true
        project_id:
          type: integer
          nullable: true
          readOnly: true
        created_date:
          type: string
          format: date-time
          readOnly: true
        updated_date:
          type: string
          format: date-time
          readOnly: true
        markdown:
          type: string
          readOnly: true
    AnnotationGuideWriteRequest:
      type: object
      properties:
        task_id:
          type: integer
          nullable: true
        project_id:
          type: integer
          nullable: true
        markdown:
          type: string
    AnnotationId:
      type: object
      properties:
        obj_id:
          type: integer
          readOnly: true
        job_id:
          type: integer
          readOnly: true
        type:
          allOf:
          - $ref: '#/components/schemas/AnnotationIdTypeEnum'
          readOnly: true
        shape_type:
          readOnly: true
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ShapeType'
          - $ref: '#/components/schemas/NullEnum'
    AnnotationIdTypeEnum:
      enum:
      - tag
      - shape
      - track
      type: string
      description: |-
        * `tag` - TAG
        * `shape` - SHAPE
        * `track` - TRACK
    AnnotationsRead:
      oneOf:
      - $ref: '#/components/schemas/LabeledData'
      - type: string
        format: binary
    AssetRead:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
          readOnly: true
        filename:
          type: string
          maxLength: 1024
        created_date:
          type: string
          format: date-time
          readOnly: true
        owner:
          $ref: '#/components/schemas/BasicUser'
        guide_id:
          type: integer
          readOnly: true
      required:
      - filename
    Attribute:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          maxLength: 64
        mutable:
          type: boolean
        input_type:
          $ref: '#/components/schemas/InputTypeEnum'
        default_value:
          type: string
          maxLength: 128
        values:
          type: array
          items:
            type: string
            maxLength: 200
      required:
      - input_type
      - mutable
      - name
      - values
    AttributeRequest:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          minLength: 1
          maxLength: 64
        mutable:
          type: boolean
        input_type:
          $ref: '#/components/schemas/InputTypeEnum'
        default_value:
          type: string
          maxLength: 128
        values:
          type: array
          items:
            type: string
            maxLength: 200
      required:
      - input_type
      - mutable
      - name
      - values
    AttributeVal:
      type: object
      properties:
        spec_id:
          type: integer
        value:
          type: string
          maxLength: 4096
      required:
      - spec_id
      - value
    AttributeValRequest:
      type: object
      properties:
        spec_id:
          type: integer
        value:
          type: string
          maxLength: 4096
      required:
      - spec_id
      - value
    BackupWriteRequest:
      oneOf:
      - $ref: '#/components/schemas/ProjectFileRequest'
      nullable: true
    BasicOrganization:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        slug:
          type: string
          readOnly: true
          pattern: ^[-a-zA-Z0-9_]+$
    BasicUser:
      type: object
      properties:
        url:
          type: string
          format: uri
          readOnly: true
        id:
          type: integer
          readOnly: true
        username:
          type: string
          description: Required. 150 characters or fewer. Letters, digits and @/./+/-/_
            only.
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          maxLength: 150
        last_name:
          type: string
          maxLength: 150
      required:
      - username
    BasicUserRequest:
      type: object
      properties:
        username:
          type: string
          minLength: 1
          description: Required. 150 characters or fewer. Letters, digits and @/./+/-/_
            only.
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          maxLength: 150
        last_name:
          type: string
          maxLength: 150
      required:
      - username
    BinaryOperation:
      type: object
      properties:
        left:
          type: string
          nullable: true
          description: The name of the data series used as the left (first) operand
            of the binary operation.
        operator:
          $ref: '#/components/schemas/OperatorEnum'
        right:
          type: string
          nullable: true
          description: The name of the data series used as the right (second) operand
            of the binary operation.
      required:
      - operator
    ChunkType:
      enum:
      - video
      - imageset
      - list
      type: string
      description: |-
        * `video` - VIDEO
        * `imageset` - IMAGESET
        * `list` - LIST
    ClientEvents:
      type: object
      properties:
        events:
          type: array
          items:
            $ref: '#/components/schemas/Event'
          default: []
        timestamp:
          type: string
          format: date-time
      required:
      - timestamp
    ClientEventsRequest:
      type: object
      properties:
        events:
          type: array
          items:
            $ref: '#/components/schemas/EventRequest'
          default: []
        previous_event:
          allOf:
          - $ref: '#/components/schemas/EventRequest'
          writeOnly: true
          nullable: true
        timestamp:
          type: string
          format: date-time
      required:
      - timestamp
    CloudStorageContent:
      type: object
      properties:
        next:
          type: string
          nullable: true
          description: This token is used to continue listing files in the bucket.
        content:
          type: array
          items:
            $ref: '#/components/schemas/FileInfo'
      required:
      - content
    CloudStorageRead:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        owner:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          nullable: true
        manifests:
          type: array
          items:
            type: string
            maxLength: 1024
          default: []
        created_date:
          type: string
          format: date-time
          readOnly: true
        updated_date:
          type: string
          format: date-time
          readOnly: true
        provider_type:
          $ref: '#/components/schemas/ProviderTypeEnum'
        resource:
          type: string
          maxLength: 222
        display_name:
          type: string
          maxLength: 63
        credentials_type:
          $ref: '#/components/schemas/CredentialsTypeEnum'
        specific_attributes:
          type: string
          maxLength: 1024
        description:
          type: string
        organization:
          type: integer
          readOnly: true
          nullable: true
      required:
      - credentials_type
      - display_name
      - provider_type
      - resource
    CloudStorageWriteRequest:
      type: object
      properties:
        provider_type:
          $ref: '#/components/schemas/ProviderTypeEnum'
        resource:
          type: string
          minLength: 1
          maxLength: 222
        display_name:
          type: string
          minLength: 1
          maxLength: 63
        owner:
          $ref: '#/components/schemas/BasicUserRequest'
        credentials_type:
          $ref: '#/components/schemas/CredentialsTypeEnum'
        session_token:
          type: string
          maxLength: 440
        account_name:
          type: string
          maxLength: 24
        key:
          type: string
          maxLength: 40
        secret_key:
          type: string
          maxLength: 64
        connection_string:
          type: string
          maxLength: 1024
        key_file:
          type: string
          format: binary
        specific_attributes:
          type: string
          maxLength: 1024
        description:
          type: string
        manifests:
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 1024
          default: []
      required:
      - credentials_type
      - display_name
      - provider_type
      - resource
    CommentRead:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        issue:
          type: integer
          readOnly: true
        owner:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          nullable: true
        message:
          type: string
          readOnly: true
        created_date:
          type: string
          format: date-time
          readOnly: true
        updated_date:
          type: string
          format: date-time
          readOnly: true
    CommentWriteRequest:
      type: object
      properties:
        issue:
          type: integer
        message:
          type: string
          minLength: 1
      required:
      - issue
    CommentsSummary:
      type: object
      properties:
        count:
          type: integer
          default: 0
        url:
          type: string
          format: uri
          readOnly: true
    CredentialsTypeEnum:
      enum:
      - KEY_SECRET_KEY_PAIR
      - ACCOUNT_NAME_TOKEN_PAIR
      - KEY_FILE_PATH
      - ANONYMOUS_ACCESS
      - CONNECTION_STRING
      type: string
      description: |-
        * `KEY_SECRET_KEY_PAIR` - KEY_SECRET_KEY_PAIR
        * `ACCOUNT_NAME_TOKEN_PAIR` - ACCOUNT_NAME_TOKEN_PAIR
        * `KEY_FILE_PATH` - KEY_FILE_PATH
        * `ANONYMOUS_ACCESS` - ANONYMOUS_ACCESS
        * `CONNECTION_STRING` - CONNECTION_STRING
    DataFrame:
      type: object
      properties:
        value:
          type: number
          format: double
        date:
          type: string
          format: date
      required:
      - date
      - value
    DataMetaRead:
      type: object
      properties:
        chunk_size:
          type: integer
          readOnly: true
          nullable: true
        size:
          type: integer
          readOnly: true
          description: |
            The number of frames included. Deleted frames do not affect this value.
        image_quality:
          type: integer
          maximum: 100
          minimum: 0
        start_frame:
          type: integer
          readOnly: true
        stop_frame:
          type: integer
          readOnly: true
        frame_filter:
          type: string
          readOnly: true
        frames:
          type: array
          items:
            $ref: '#/components/schemas/FrameMeta'
          nullable: true
        deleted_frames:
          type: array
          items:
            type: integer
            minimum: 0
        included_frames:
          type: array
          items:
            type: integer
            minimum: 0
          nullable: true
          description: |
            A list of valid frame ids. The None value means all frames are included.
      required:
      - deleted_frames
      - frames
      - image_quality
    DataRequest:
      type: object
      description: |-
        Read more about parameters here:
        https://docs.cvat.ai/docs/manual/basics/create_an_annotation_task/#advanced-configuration
      properties:
        chunk_size:
          type: integer
          maximum: 2147483647
          minimum: 0
          nullable: true
          description: Maximum number of frames per chunk
        image_quality:
          type: integer
          maximum: 100
          minimum: 0
          description: Image quality to use during annotation
        start_frame:
          type: integer
          maximum: 2147483647
          minimum: 0
          description: First frame index
        stop_frame:
          type: integer
          maximum: 2147483647
          minimum: 0
          description: Last frame index
        frame_filter:
          type: string
          description: 'Frame filter. The only supported syntax is: ''step=N'''
          maxLength: 256
        client_files:
          type: array
          items:
            type: string
            format: binary
          default: []
          description: |2

            Uploaded files.
            Must contain all files from job_file_mapping if job_file_mapping is not empty.
        server_files:
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 1024
          default: []
          description: |2

            Paths to files from a file share mounted on the server, or from a cloud storage.
            Must contain all files from job_file_mapping if job_file_mapping is not empty.
        remote_files:
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 1024
          default: []
          description: |2

            Direct download URLs for files.
            Must contain all files from job_file_mapping if job_file_mapping is not empty.
        use_zip_chunks:
          type: boolean
          default: false
          description: |
            When true, video chunks will be represented as zip archives with decoded video frames.
            When false, video chunks are represented as video segments
        server_files_exclude:
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 1024
          default: []
          description: |
            Paths to files and directories from a file share mounted on the server, or from a cloud storage
            that should be excluded from the directories specified in server_files.
            This option cannot be used together with filename_pattern.
            The server_files_exclude parameter cannot be used to exclude a part of dataset from an archive.

            Examples:

            Exclude all files from subfolder 'sub/sub_1/sub_2'and single file 'sub/image.jpg' from specified folder:
            server_files = ['sub/'], server_files_exclude = ['sub/sub_1/sub_2/', 'sub/image.jpg']

            Exclude all cloud storage files with prefix 'sub' from the content of manifest file:
            server_files = ['manifest.jsonl'], server_files_exclude = ['sub/']
        cloud_storage_id:
          type: integer
          writeOnly: true
          nullable: true
          description: |
            If not null, the files referenced by server_files will be retrieved
            from the cloud storage with the specified ID.
            The cloud storages applicable depend on the context.
            In the user sandbox, only the user sandbox cloud storages can be used.
            In an organization, only the organization cloud storages can be used.
        use_cache:
          type: boolean
          default: false
          description: |
            Enable or disable task data chunk caching for the task.
            Read more: https://docs.cvat.ai/docs/manual/advanced/data_on_fly/
        copy_data:
          type: boolean
          default: false
          description: |
            Copy data from the server file share to CVAT during the task creation.
            This will create a copy of the data, making the server independent from
            the file share availability
        storage_method:
          $ref: '#/components/schemas/StorageMethod'
        storage:
          $ref: '#/components/schemas/StorageType'
        sorting_method:
          $ref: '#/components/schemas/SortingMethod'
        filename_pattern:
          type: string
          nullable: true
          minLength: 1
          description: |
            A filename filter for cloud storage files
            listed in the manifest. Supports fnmatch wildcards.
            Read more: https://docs.python.org/3/library/fnmatch.html
        job_file_mapping:
          type: array
          items:
            type: array
            items:
              type: string
              minLength: 1
              maxLength: 1024
          writeOnly: true
          description: |2

            Represents a file-to-job mapping.
            Useful to specify a custom job configuration during task creation.
            This option is not compatible with most other job split-related options.
            Files in the jobs must not overlap or repeat.
            Job file mapping files must be a subset of the input files.
            If directories are specified in server_files, all files obtained by recursive search
            in the specified directories will be used as input files.
            In case of missing items in the input files, an error will be raised.

            Example:
            [

                ["file1.jpg", "file2.jpg"], # job #1 files
                ["file3.png"], # job #2 files
                ["file4.jpg", "file5.png", "file6.bmp"], # job #3 files
            ]
        upload_file_order:
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 1024
          writeOnly: true
          description: |
            Allows to specify file order for client_file uploads.
            Only valid with the "predefined" sorting method selected.

            To state that the input files are sent in the correct order,
            pass an empty list.

            If you want to send files in an arbitrary order
            and reorder them afterwards on the server,
            pass the list of file names in the required order.
      required:
      - image_quality
    DatasetFileRequest:
      type: object
      properties:
        dataset_file:
          type: string
          format: binary
      required:
      - dataset_file
    DatasetFormat:
      type: object
      properties:
        name:
          type: string
          maxLength: 64
        ext:
          type: string
          maxLength: 64
        version:
          type: string
          maxLength: 64
        enabled:
          type: boolean
        dimension:
          type: string
          maxLength: 2
      required:
      - dimension
      - enabled
      - ext
      - name
      - version
    DatasetFormats:
      type: object
      properties:
        importers:
          type: array
          items:
            $ref: '#/components/schemas/DatasetFormat'
        exporters:
          type: array
          items:
            $ref: '#/components/schemas/DatasetFormat'
      required:
      - exporters
      - importers
    DatasetWriteRequest:
      oneOf:
      - $ref: '#/components/schemas/DatasetFileRequest'
      nullable: true
    DefaultViewEnum:
      enum:
      - numeric
      - histogram
      type: string
      description: |-
        * `numeric` - NUMERIC
        * `histogram` - HISTOGRAM
    Event:
      type: object
      properties:
        scope:
          type: string
        obj_name:
          type: string
          nullable: true
        obj_id:
          type: integer
          nullable: true
        obj_val:
          type: string
          nullable: true
        source:
          type: string
          nullable: true
        timestamp:
          type: string
          format: date-time
        count:
          type: integer
          nullable: true
        duration:
          type: integer
          default: 0
        project_id:
          type: integer
          nullable: true
        task_id:
          type: integer
          nullable: true
        job_id:
          type: integer
          nullable: true
        user_id:
          type: integer
          nullable: true
        user_name:
          type: string
          nullable: true
        user_email:
          type: string
          nullable: true
        org_id:
          type: integer
          nullable: true
        org_slug:
          type: string
          nullable: true
        payload:
          type: string
          nullable: true
      required:
      - scope
      - timestamp
    EventRequest:
      type: object
      properties:
        scope:
          type: string
          minLength: 1
        obj_name:
          type: string
          nullable: true
          minLength: 1
        obj_id:
          type: integer
          nullable: true
        obj_val:
          type: string
          nullable: true
          minLength: 1
        source:
          type: string
          nullable: true
          minLength: 1
        timestamp:
          type: string
          format: date-time
        count:
          type: integer
          nullable: true
        duration:
          type: integer
          default: 0
        project_id:
          type: integer
          nullable: true
        task_id:
          type: integer
          nullable: true
        job_id:
          type: integer
          nullable: true
        user_id:
          type: integer
          nullable: true
        user_name:
          type: string
          nullable: true
          minLength: 1
        user_email:
          type: string
          nullable: true
          minLength: 1
        org_id:
          type: integer
          nullable: true
        org_slug:
          type: string
          nullable: true
          minLength: 1
        payload:
          type: string
          nullable: true
          minLength: 1
      required:
      - scope
      - timestamp
    Events:
      type: object
      properties:
        webhook_type:
          $ref: '#/components/schemas/WebhookType'
        events:
          type: array
          items:
            $ref: '#/components/schemas/EventsEnum'
      required:
      - events
      - webhook_type
    EventsEnum:
      enum:
      - create:comment
      - create:invitation
      - create:issue
      - create:job
      - create:membership
      - create:project
      - create:task
      - delete:comment
      - delete:invitation
      - delete:issue
      - delete:job
      - delete:membership
      - delete:organization
      - delete:project
      - delete:task
      - update:comment
      - update:issue
      - update:job
      - update:membership
      - update:organization
      - update:project
      - update:task
      type: string
      description: |-
        * `create:comment` - CREATE:COMMENT
        * `create:invitation` - CREATE:INVITATION
        * `create:issue` - CREATE:ISSUE
        * `create:job` - CREATE:JOB
        * `create:membership` - CREATE:MEMBERSHIP
        * `create:project` - CREATE:PROJECT
        * `create:task` - CREATE:TASK
        * `delete:comment` - DELETE:COMMENT
        * `delete:invitation` - DELETE:INVITATION
        * `delete:issue` - DELETE:ISSUE
        * `delete:job` - DELETE:JOB
        * `delete:membership` - DELETE:MEMBERSHIP
        * `delete:organization` - DELETE:ORGANIZATION
        * `delete:project` - DELETE:PROJECT
        * `delete:task` - DELETE:TASK
        * `update:comment` - UPDATE:COMMENT
        * `update:issue` - UPDATE:ISSUE
        * `update:job` - UPDATE:JOB
        * `update:membership` - UPDATE:MEMBERSHIP
        * `update:organization` - UPDATE:ORGANIZATION
        * `update:project` - UPDATE:PROJECT
        * `update:task` - UPDATE:TASK
    FileInfo:
      type: object
      properties:
        name:
          type: string
          maxLength: 1024
        type:
          $ref: '#/components/schemas/FileInfoTypeEnum'
        mime_type:
          type: string
          maxLength: 255
      required:
      - mime_type
      - name
      - type
    FileInfoTypeEnum:
      enum:
      - REG
      - DIR
      type: string
      description: |-
        * `REG` - REG
        * `DIR` - DIR
    FrameMeta:
      type: object
      properties:
        width:
          type: integer
        height:
          type: integer
        name:
          type: string
          maxLength: 1024
        related_files:
          type: integer
        has_related_context:
          type: boolean
          readOnly: true
      required:
      - height
      - name
      - related_files
      - width
    FrameSelectionMethodEnum:
      enum:
      - random_uniform
      - manual
      type: string
      description: |-
        * `random_uniform` - RANDOM_UNIFORM
        * `manual` - MANUAL
    FunctionCall:
      type: object
      properties:
        id:
          type: string
          description: Request id
        function:
          $ref: '#/components/schemas/FunctionCallParams'
        status:
          type: string
          nullable: true
        progress:
          type: integer
          nullable: true
          default: 0
        enqueued:
          type: string
          format: date-time
          nullable: true
        started:
          type: string
          format: date-time
          nullable: true
        ended:
          type: string
          format: date-time
          nullable: true
        exc_info:
          type: string
          nullable: true
      required:
      - ended
      - enqueued
      - function
      - id
      - started
      - status
    FunctionCallParams:
      type: object
      properties:
        id:
          type: string
          nullable: true
          description: The name of the function
        task:
          type: integer
          nullable: true
          description: The id of the task
        job:
          type: integer
          description: The id of the job
        threshold:
          type: number
          format: double
          nullable: true
      required:
      - id
      - task
      - threshold
    FunctionCallRequest:
      type: object
      properties:
        function:
          type: string
          minLength: 1
          description: The name of the function to execute
        task:
          type: integer
          description: The id of the task to be annotated
        job:
          type: integer
          description: The id of the job to be annotated
        quality:
          allOf:
          - $ref: '#/components/schemas/QualityEnum'
          default: original
          description: |-
            The quality of the images to use in the model run

            * `compressed` - compressed
            * `original` - original
        max_distance:
          type: integer
        threshold:
          type: number
          format: double
        cleanup:
          type: boolean
          default: false
          description: Whether existing annotations should be removed
        convMaskToPoly:
          type: boolean
          default: false
        mapping:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/LabelMappingEntryRequest'
          description: Label mapping from the model to the task labels
      required:
      - function
      - task
    GranularityEnum:
      enum:
      - day
      - week
      - month
      type: string
      description: |-
        * `day` - DAY
        * `week` - WEEK
        * `month` - MONTH
    InputTypeEnum:
      enum:
      - checkbox
      - radio
      - number
      - text
      - select
      type: string
      description: |-
        * `checkbox` - CHECKBOX
        * `radio` - RADIO
        * `number` - NUMBER
        * `text` - TEXT
        * `select` - SELECT
    InvitationRead:
      type: object
      properties:
        key:
          type: string
          readOnly: true
        created_date:
          type: string
          format: date-time
          readOnly: true
        owner:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          nullable: true
        role:
          $ref: '#/components/schemas/RoleEnum'
        user:
          $ref: '#/components/schemas/BasicUser'
        organization:
          type: integer
        expired:
          type: boolean
          readOnly: true
          nullable: true
        organization_info:
          $ref: '#/components/schemas/BasicOrganization'
      required:
      - organization
      - organization_info
      - owner
      - role
      - user
    InvitationWriteRequest:
      type: object
      properties:
        role:
          $ref: '#/components/schemas/RoleEnum'
        email:
          type: string
          format: email
          minLength: 1
      required:
      - email
      - role
    IssueRead:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        frame:
          type: integer
          readOnly: true
        position:
          type: array
          items:
            type: number
            format: double
        job:
          type: integer
          readOnly: true
        owner:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          nullable: true
        assignee:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          nullable: true
        created_date:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        updated_date:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        resolved:
          type: boolean
          readOnly: true
        comments:
          $ref: '#/components/schemas/CommentsSummary'
      required:
      - comments
      - position
    IssueWriteRequest:
      type: object
      properties:
        frame:
          type: integer
          maximum: 2147483647
          minimum: 0
        position:
          type: array
          items:
            type: number
            format: double
        job:
          type: integer
        assignee:
          type: integer
          nullable: true
        message:
          type: string
          minLength: 1
        resolved:
          type: boolean
      required:
      - frame
      - job
      - message
      - position
    IssuesSummary:
      type: object
      properties:
        url:
          type: string
          format: uri
          readOnly: true
        count:
          type: integer
          readOnly: true
    JobAnnotationsUpdateRequest:
      oneOf:
      - $ref: '#/components/schemas/LabeledDataRequest'
      - $ref: '#/components/schemas/AnnotationFileRequest'
    JobRead:
      type: object
      properties:
        url:
          type: string
          format: uri
          readOnly: true
        id:
          type: integer
          readOnly: true
        task_id:
          type: integer
          readOnly: true
        project_id:
          type: integer
          readOnly: true
          nullable: true
        assignee:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          readOnly: true
          nullable: true
        guide_id:
          type: integer
          readOnly: true
          nullable: true
        dimension:
          type: string
          readOnly: true
          maxLength: 2
        bug_tracker:
          type: string
          readOnly: true
          nullable: true
          maxLength: 2000
        status:
          allOf:
          - $ref: '#/components/schemas/JobStatus'
          readOnly: true
        stage:
          allOf:
          - $ref: '#/components/schemas/JobStage'
          readOnly: true
        state:
          allOf:
          - $ref: '#/components/schemas/OperationStatus'
          readOnly: true
        mode:
          type: string
          readOnly: true
        frame_count:
          type: integer
          readOnly: true
        start_frame:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          readOnly: true
        stop_frame:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          readOnly: true
        data_chunk_size:
          type: integer
          maximum: 2147483647
          minimum: 0
          nullable: true
          readOnly: true
        data_compressed_chunk_type:
          allOf:
          - $ref: '#/components/schemas/ChunkType'
          readOnly: true
        created_date:
          type: string
          format: date-time
          readOnly: true
        updated_date:
          type: string
          format: date-time
          readOnly: true
        issues:
          $ref: '#/components/schemas/IssuesSummary'
        labels:
          $ref: '#/components/schemas/LabelsSummary'
        type:
          allOf:
          - $ref: '#/components/schemas/JobType'
          readOnly: true
        organization:
          type: integer
          readOnly: true
          nullable: true
        target_storage:
          allOf:
          - $ref: '#/components/schemas/Storage'
          nullable: true
        source_storage:
          allOf:
          - $ref: '#/components/schemas/Storage'
          nullable: true
      required:
      - issues
      - labels
    JobStage:
      enum:
      - annotation
      - validation
      - acceptance
      type: string
      description: |-
        * `annotation` - ANNOTATION
        * `validation` - VALIDATION
        * `acceptance` - ACCEPTANCE
    JobStatus:
      enum:
      - annotation
      - validation
      - completed
      type: string
      description: |-
        * `annotation` - ANNOTATION
        * `validation` - VALIDATION
        * `completed` - COMPLETED
    JobType:
      enum:
      - annotation
      - ground_truth
      type: string
      description: |-
        * `annotation` - ANNOTATION
        * `ground_truth` - GROUND_TRUTH
    JobWriteRequest:
      type: object
      properties:
        assignee:
          type: integer
          nullable: true
        stage:
          $ref: '#/components/schemas/JobStage'
        state:
          $ref: '#/components/schemas/OperationStatus'
        type:
          $ref: '#/components/schemas/JobType'
        task_id:
          type: integer
        frame_selection_method:
          $ref: '#/components/schemas/FrameSelectionMethodEnum'
        frame_count:
          type: integer
          minimum: 0
          description: |
            The number of frames included in the job.
            Applicable only to the random frame selection
        seed:
          type: integer
          minimum: 0
          description: |
            The seed value for the random number generator.
            The same value will produce the same frame sets.
            Applicable only to the random frame selection.
            By default, a random value is used.
        frames:
          type: array
          items:
            type: integer
            minimum: 0
          description: |
            The list of frame ids. Applicable only to the manual frame selection
      required:
      - task_id
      - type
    JobsSummary:
      type: object
      properties:
        count:
          type: integer
          default: 0
        completed:
          type: integer
          nullable: true
        validation:
          type: integer
          nullable: true
        url:
          type: string
          format: uri
          readOnly: true
      required:
      - completed
      - validation
    Label:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          maxLength: 64
        color:
          type: string
          description: The hex value for the RGB color. Will be generated automatically,
            unless specified explicitly.
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/Attribute'
          default: []
          description: The list of attributes. If you want to remove an attribute,
            you need to recreate the label and specify the remaining attributes.
        type:
          type: string
          description: Associated annotation type for this label
        svg:
          type: string
        sublabels:
          type: array
          items:
            $ref: '#/components/schemas/Sublabel'
        project_id:
          type: integer
          nullable: true
          readOnly: true
        task_id:
          type: integer
          nullable: true
          readOnly: true
        parent_id:
          type: integer
          nullable: true
          readOnly: true
        has_parent:
          type: boolean
          readOnly: true
      required:
      - name
    LabelMappingEntryRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
        attributes:
          type: object
          additionalProperties:
            type: string
            minLength: 1
        sublabels:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/SublabelMappingEntryRequest'
          description: Label mapping for from the model to the task sublabels within
            a parent label
      required:
      - name
    LabeledData:
      type: object
      properties:
        version:
          type: integer
          default: 0
        tags:
          type: array
          items:
            $ref: '#/components/schemas/LabeledImage'
          default: []
        shapes:
          type: array
          items:
            $ref: '#/components/schemas/LabeledShape'
          default: []
        tracks:
          type: array
          items:
            $ref: '#/components/schemas/LabeledTrack'
          default: []
    LabeledDataRequest:
      type: object
      properties:
        version:
          type: integer
          default: 0
        tags:
          type: array
          items:
            $ref: '#/components/schemas/LabeledImageRequest'
          default: []
        shapes:
          type: array
          items:
            $ref: '#/components/schemas/LabeledShapeRequest'
          default: []
        tracks:
          type: array
          items:
            $ref: '#/components/schemas/LabeledTrackRequest'
          default: []
    LabeledImage:
      type: object
      properties:
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        label_id:
          type: integer
          minimum: 0
        group:
          type: integer
          minimum: 0
          nullable: true
        source:
          type: string
          default: manual
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeVal'
          default: []
      required:
      - frame
      - label_id
    LabeledImageRequest:
      type: object
      properties:
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        label_id:
          type: integer
          minimum: 0
        group:
          type: integer
          minimum: 0
          nullable: true
        source:
          type: string
          minLength: 1
          default: manual
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeValRequest'
          default: []
      required:
      - frame
      - label_id
    LabeledShape:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/ShapeType'
        occluded:
          type: boolean
          default: false
        outside:
          type: boolean
          default: false
        z_order:
          type: integer
          default: 0
        rotation:
          type: number
          format: double
          maximum: 360
          minimum: 0
          default: 0.0
        points:
          type: array
          items:
            type: number
            format: double
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        label_id:
          type: integer
          minimum: 0
        group:
          type: integer
          minimum: 0
          nullable: true
        source:
          type: string
          default: manual
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeVal'
          default: []
        elements:
          type: array
          items:
            $ref: '#/components/schemas/SubLabeledShape'
      required:
      - frame
      - label_id
      - type
    LabeledShapeRequest:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/ShapeType'
        occluded:
          type: boolean
          default: false
        outside:
          type: boolean
          default: false
        z_order:
          type: integer
          default: 0
        rotation:
          type: number
          format: double
          maximum: 360
          minimum: 0
          default: 0.0
        points:
          type: array
          items:
            type: number
            format: double
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        label_id:
          type: integer
          minimum: 0
        group:
          type: integer
          minimum: 0
          nullable: true
        source:
          type: string
          minLength: 1
          default: manual
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeValRequest'
          default: []
        elements:
          type: array
          items:
            $ref: '#/components/schemas/SubLabeledShapeRequest'
      required:
      - frame
      - label_id
      - type
    LabeledTrack:
      type: object
      properties:
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        label_id:
          type: integer
          minimum: 0
        group:
          type: integer
          minimum: 0
          nullable: true
        source:
          type: string
          default: manual
        shapes:
          type: array
          items:
            $ref: '#/components/schemas/TrackedShape'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeVal'
          default: []
        elements:
          type: array
          items:
            $ref: '#/components/schemas/SubLabeledTrack'
      required:
      - frame
      - label_id
      - shapes
    LabeledTrackRequest:
      type: object
      properties:
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        label_id:
          type: integer
          minimum: 0
        group:
          type: integer
          minimum: 0
          nullable: true
        source:
          type: string
          minLength: 1
          default: manual
        shapes:
          type: array
          items:
            $ref: '#/components/schemas/TrackedShapeRequest'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeValRequest'
          default: []
        elements:
          type: array
          items:
            $ref: '#/components/schemas/SubLabeledTrackRequest'
      required:
      - frame
      - label_id
      - shapes
    LabelsSummary:
      type: object
      properties:
        url:
          type: string
          format: uri
          readOnly: true
    LocationEnum:
      enum:
      - cloud_storage
      - local
      type: string
      description: |-
        * `cloud_storage` - CLOUD_STORAGE
        * `local` - LOCAL
    LoginSerializerExRequest:
      type: object
      properties:
        username:
          type: string
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 1
      required:
      - password
    MembershipRead:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        user:
          $ref: '#/components/schemas/BasicUser'
        organization:
          type: integer
          readOnly: true
        is_active:
          type: boolean
          readOnly: true
        joined_date:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        role:
          allOf:
          - $ref: '#/components/schemas/RoleEnum'
          readOnly: true
        invitation:
          type: string
          readOnly: true
          nullable: true
      required:
      - user
    MetaUser:
      anyOf:
      - $ref: '#/components/schemas/User'
      - $ref: '#/components/schemas/BasicUser'
    Metric:
      type: object
      properties:
        name:
          type: string
        title:
          type: string
        description:
          type: string
        granularity:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/GranularityEnum'
          - $ref: '#/components/schemas/NullEnum'
        default_view:
          $ref: '#/components/schemas/DefaultViewEnum'
        data_series:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/DataFrame'
        transformations:
          type: array
          items:
            $ref: '#/components/schemas/Transformation'
      required:
      - data_series
      - default_view
      - description
      - name
      - title
      - transformations
    NullEnum:
      enum:
      - null
    OnlineFunctionCallRequest:
      type: object
      properties:
        job:
          type: integer
        task:
          type: integer
    OperationStatus:
      enum:
      - new
      - in progress
      - completed
      - rejected
      type: string
      description: |-
        * `new` - NEW
        * `in progress` - IN_PROGRESS
        * `completed` - COMPLETED
        * `rejected` - REJECTED
    OperatorEnum:
      enum:
      - +
      - '-'
      - '*'
      - /
      type: string
      description: |-
        * `+` - ADDITION
        * `-` - SUBTRACTION
        * `*` - MULTIPLICATION
        * `/` - DIVISION
    OrganizationRead:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        slug:
          type: string
          readOnly: true
          pattern: ^[-a-zA-Z0-9_]+$
        name:
          type: string
          readOnly: true
        description:
          type: string
          readOnly: true
        created_date:
          type: string
          format: date-time
          readOnly: true
        updated_date:
          type: string
          format: date-time
          readOnly: true
        contact:
          type: object
          readOnly: true
        owner:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          nullable: true
      required:
      - owner
    OrganizationWriteRequest:
      type: object
      properties:
        slug:
          type: string
          minLength: 1
          maxLength: 16
          pattern: ^[-a-zA-Z0-9_]+$
        name:
          type: string
          maxLength: 64
        description:
          type: string
        contact:
          type: object
      required:
      - slug
    PaginatedAnnotationConflictList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/AnnotationConflict'
    PaginatedCloudStorageReadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/CloudStorageRead'
    PaginatedCommentReadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/CommentRead'
    PaginatedInvitationReadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/InvitationRead'
    PaginatedIssueReadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/IssueRead'
    PaginatedJobReadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/JobRead'
    PaginatedLabelList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Label'
    PaginatedMembershipReadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/MembershipRead'
    PaginatedMetaUserList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/MetaUser'
    PaginatedOrganizationReadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/OrganizationRead'
    PaginatedProjectReadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ProjectRead'
    PaginatedQualityReportList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/QualityReport'
    PaginatedQualitySettingsList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/QualitySettings'
    PaginatedTaskReadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TaskRead'
    PaginatedWebhookDeliveryReadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/WebhookDeliveryRead'
    PaginatedWebhookReadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/WebhookRead'
    PasswordChangeRequest:
      type: object
      properties:
        old_password:
          type: string
          minLength: 1
          maxLength: 128
        new_password1:
          type: string
          minLength: 1
          maxLength: 128
        new_password2:
          type: string
          minLength: 1
          maxLength: 128
      required:
      - new_password1
      - new_password2
      - old_password
    PasswordResetConfirmRequest:
      type: object
      description: Serializer for confirming a password reset attempt.
      properties:
        new_password1:
          type: string
          minLength: 1
          maxLength: 128
        new_password2:
          type: string
          minLength: 1
          maxLength: 128
        uid:
          type: string
          minLength: 1
        token:
          type: string
          minLength: 1
      required:
      - new_password1
      - new_password2
      - token
      - uid
    PasswordResetSerializerExRequest:
      type: object
      description: Serializer for requesting a password reset e-mail.
      properties:
        email:
          type: string
          format: email
          minLength: 1
      required:
      - email
    PatchedAnnotationGuideWriteRequest:
      type: object
      properties:
        task_id:
          type: integer
          nullable: true
        project_id:
          type: integer
          nullable: true
        markdown:
          type: string
    PatchedCloudStorageWriteRequest:
      type: object
      properties:
        provider_type:
          $ref: '#/components/schemas/ProviderTypeEnum'
        resource:
          type: string
          minLength: 1
          maxLength: 222
        display_name:
          type: string
          minLength: 1
          maxLength: 63
        owner:
          $ref: '#/components/schemas/BasicUserRequest'
        credentials_type:
          $ref: '#/components/schemas/CredentialsTypeEnum'
        session_token:
          type: string
          maxLength: 440
        account_name:
          type: string
          maxLength: 24
        key:
          type: string
          maxLength: 40
        secret_key:
          type: string
          maxLength: 64
        connection_string:
          type: string
          maxLength: 1024
        key_file:
          type: string
          format: binary
        specific_attributes:
          type: string
          maxLength: 1024
        description:
          type: string
        manifests:
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 1024
          default: []
    PatchedCommentWriteRequest:
      type: object
      properties:
        message:
          type: string
          minLength: 1
    PatchedDataMetaWriteRequest:
      type: object
      properties:
        deleted_frames:
          type: array
          items:
            type: integer
            minimum: 0
    PatchedInvitationWriteRequest:
      type: object
      properties:
        role:
          $ref: '#/components/schemas/RoleEnum'
        email:
          type: string
          format: email
          minLength: 1
    PatchedIssueWriteRequest:
      type: object
      properties:
        position:
          type: array
          items:
            type: number
            format: double
        assignee:
          type: integer
          nullable: true
        resolved:
          type: boolean
    PatchedJobWriteRequest:
      type: object
      properties:
        assignee:
          type: integer
          nullable: true
        stage:
          $ref: '#/components/schemas/JobStage'
        state:
          $ref: '#/components/schemas/OperationStatus'
    PatchedLabelRequest:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          minLength: 1
          maxLength: 64
        color:
          type: string
          description: The hex value for the RGB color. Will be generated automatically,
            unless specified explicitly.
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeRequest'
          default: []
          description: The list of attributes. If you want to remove an attribute,
            you need to recreate the label and specify the remaining attributes.
        deleted:
          type: boolean
          writeOnly: true
          description: Delete the label. Only applicable in the PATCH methods of a
            project or a task.
        type:
          type: string
          description: Associated annotation type for this label
        svg:
          type: string
        sublabels:
          type: array
          items:
            $ref: '#/components/schemas/SublabelRequest'
    PatchedLabeledDataRequest:
      type: object
      properties:
        version:
          type: integer
          default: 0
        tags:
          type: array
          items:
            $ref: '#/components/schemas/LabeledImageRequest'
          default: []
        shapes:
          type: array
          items:
            $ref: '#/components/schemas/LabeledShapeRequest'
          default: []
        tracks:
          type: array
          items:
            $ref: '#/components/schemas/LabeledTrackRequest'
          default: []
    PatchedMembershipWriteRequest:
      type: object
      properties:
        role:
          $ref: '#/components/schemas/RoleEnum'
    PatchedOrganizationWriteRequest:
      type: object
      properties:
        slug:
          type: string
          minLength: 1
          maxLength: 16
          pattern: ^[-a-zA-Z0-9_]+$
        name:
          type: string
          maxLength: 64
        description:
          type: string
        contact:
          type: object
    PatchedProjectWriteRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 256
        labels:
          type: array
          items:
            $ref: '#/components/schemas/PatchedLabelRequest'
          writeOnly: true
          default: []
        owner_id:
          type: integer
          writeOnly: true
          nullable: true
        assignee_id:
          type: integer
          writeOnly: true
          nullable: true
        bug_tracker:
          type: string
          maxLength: 2000
        target_storage:
          allOf:
          - $ref: '#/components/schemas/StorageRequest'
          writeOnly: true
        source_storage:
          allOf:
          - $ref: '#/components/schemas/StorageRequest'
          writeOnly: true
        task_subsets:
          type: array
          items:
            type: string
            minLength: 1
          writeOnly: true
    PatchedQualitySettingsRequest:
      type: object
      properties:
        iou_threshold:
          type: number
          format: double
          description: Used for distinction between matched / unmatched shapes
        oks_sigma:
          type: number
          format: double
          description: |
            Like IoU threshold, but for points.
            The percent of the bbox area, used as the radius of the circle around the GT point,
            where the checked point is expected to be.
            Read more: https://cocodataset.org/#keypoints-eval
        line_thickness:
          type: number
          format: double
          description: |
            Thickness of polylines, relatively to the (image area) ^ 0.5.
            The distance to the boundary around the GT line,
            inside of which the checked line points should be
        low_overlap_threshold:
          type: number
          format: double
          description: |
            Used for distinction between strong / weak (low_overlap) matches
        compare_line_orientation:
          type: boolean
          description: Enables or disables polyline orientation comparison
        line_orientation_threshold:
          type: number
          format: double
          description: |
            The minimal gain in the GT IoU between the given and reversed line directions
            to consider the line inverted.
            Only used when the 'compare_line_orientation' parameter is true
        compare_groups:
          type: boolean
          description: Enables or disables annotation group checks
        group_match_threshold:
          type: number
          format: double
          description: |
            Minimal IoU for groups to be considered matching.
            Only used when the 'compare_groups' parameter is true
        check_covered_annotations:
          type: boolean
          description: |
            Check for partially-covered annotations, useful in segmentation tasks
        object_visibility_threshold:
          type: number
          format: double
          description: |
            Minimal visible area percent of the spatial annotations (polygons, masks)
            for reporting covered annotations.
            Only used when the 'object_visibility_threshold' parameter is true
        panoptic_comparison:
          type: boolean
          description: |
            Use only the visible part of the masks and polygons in comparisons
        compare_attributes:
          type: boolean
          description: Enables or disables annotation attribute comparison
    PatchedTaskWriteRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 256
        project_id:
          type: integer
          nullable: true
        owner_id:
          type: integer
          writeOnly: true
          nullable: true
        assignee_id:
          type: integer
          writeOnly: true
          nullable: true
        bug_tracker:
          type: string
          maxLength: 2000
        labels:
          type: array
          items:
            $ref: '#/components/schemas/PatchedLabelRequest'
        subset:
          type: string
          maxLength: 64
        target_storage:
          allOf:
          - $ref: '#/components/schemas/StorageRequest'
          nullable: true
        source_storage:
          allOf:
          - $ref: '#/components/schemas/StorageRequest'
          nullable: true
    PatchedUserRequest:
      type: object
      properties:
        username:
          type: string
          minLength: 1
          description: Required. 150 characters or fewer. Letters, digits and @/./+/-/_
            only.
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          maxLength: 150
        last_name:
          type: string
          maxLength: 150
        email:
          type: string
          format: email
          title: Email address
          maxLength: 254
        groups:
          type: array
          items:
            type: string
            minLength: 1
        is_staff:
          type: boolean
          title: Staff status
          description: Designates whether the user can log into this admin site.
        is_superuser:
          type: boolean
          title: Superuser status
          description: Designates that this user has all permissions without explicitly
            assigning them.
        is_active:
          type: boolean
          title: Active
          description: Designates whether this user should be treated as active. Unselect
            this instead of deleting accounts.
    PatchedWebhookWriteRequest:
      type: object
      properties:
        target_url:
          type: string
          format: uri
          minLength: 1
          maxLength: 8192
        description:
          type: string
          maxLength: 128
        content_type:
          $ref: '#/components/schemas/WebhookContentType'
        secret:
          type: string
          maxLength: 64
        is_active:
          type: boolean
        enable_ssl:
          type: boolean
        events:
          type: array
          items:
            $ref: '#/components/schemas/EventsEnum'
          writeOnly: true
    Plugins:
      type: object
      properties:
        GIT_INTEGRATION:
          type: boolean
        ANALYTICS:
          type: boolean
        MODELS:
          type: boolean
        PREDICT:
          type: boolean
      required:
      - ANALYTICS
      - GIT_INTEGRATION
      - MODELS
      - PREDICT
    ProjectFileRequest:
      type: object
      properties:
        project_file:
          type: string
          format: binary
      required:
      - project_file
    ProjectRead:
      type: object
      properties:
        url:
          type: string
          format: uri
          readOnly: true
        id:
          type: integer
          readOnly: true
        name:
          type: string
          readOnly: true
        owner:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          readOnly: true
          nullable: true
        assignee:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          readOnly: true
          nullable: true
        guide_id:
          type: integer
          nullable: true
        bug_tracker:
          type: string
          readOnly: true
        task_subsets:
          type: array
          items:
            type: string
          readOnly: true
        created_date:
          type: string
          format: date-time
          readOnly: true
        updated_date:
          type: string
          format: date-time
          readOnly: true
        status:
          allOf:
          - $ref: '#/components/schemas/JobStatus'
          readOnly: true
        dimension:
          type: string
          readOnly: true
          nullable: true
          maxLength: 16
        organization:
          type: integer
          readOnly: true
          nullable: true
        target_storage:
          allOf:
          - $ref: '#/components/schemas/Storage'
          readOnly: true
          nullable: true
        source_storage:
          allOf:
          - $ref: '#/components/schemas/Storage'
          readOnly: true
          nullable: true
        tasks:
          $ref: '#/components/schemas/TasksSummary'
        labels:
          $ref: '#/components/schemas/LabelsSummary'
      required:
      - labels
      - tasks
    ProjectWriteRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 256
        labels:
          type: array
          items:
            $ref: '#/components/schemas/PatchedLabelRequest'
          writeOnly: true
          default: []
        owner_id:
          type: integer
          writeOnly: true
          nullable: true
        assignee_id:
          type: integer
          writeOnly: true
          nullable: true
        bug_tracker:
          type: string
          maxLength: 2000
        target_storage:
          allOf:
          - $ref: '#/components/schemas/StorageRequest'
          writeOnly: true
        source_storage:
          allOf:
          - $ref: '#/components/schemas/StorageRequest'
          writeOnly: true
        task_subsets:
          type: array
          items:
            type: string
            minLength: 1
          writeOnly: true
      required:
      - name
    ProviderTypeEnum:
      enum:
      - AWS_S3_BUCKET
      - AZURE_CONTAINER
      - GOOGLE_DRIVE
      - GOOGLE_CLOUD_STORAGE
      type: string
      description: |-
        * `AWS_S3_BUCKET` - AWS_S3
        * `AZURE_CONTAINER` - AZURE_CONTAINER
        * `GOOGLE_DRIVE` - GOOGLE_DRIVE
        * `GOOGLE_CLOUD_STORAGE` - GOOGLE_CLOUD_STORAGE
    QualityEnum:
      enum:
      - compressed
      - original
      type: string
      description: |-
        * `compressed` - compressed
        * `original` - original
    QualityReport:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        job_id:
          type: integer
          nullable: true
          readOnly: true
        task_id:
          type: integer
          nullable: true
          readOnly: true
        parent_id:
          type: integer
          nullable: true
          readOnly: true
        target:
          $ref: '#/components/schemas/QualityReportTarget'
        summary:
          $ref: '#/components/schemas/QualityReportSummary'
        created_date:
          type: string
          format: date-time
          readOnly: true
        target_last_updated:
          type: string
          format: date-time
          readOnly: true
        gt_last_updated:
          type: string
          format: date-time
          readOnly: true
      required:
      - summary
      - target
    QualityReportCreateRequest:
      type: object
      properties:
        task_id:
          type: integer
          writeOnly: true
      required:
      - task_id
    QualityReportSummary:
      type: object
      properties:
        frame_count:
          type: integer
        frame_share:
          type: number
          format: double
        conflict_count:
          type: integer
        warning_count:
          type: integer
        error_count:
          type: integer
        conflicts_by_type:
          type: object
          additionalProperties:
            type: integer
        valid_count:
          type: integer
        ds_count:
          type: integer
        gt_count:
          type: integer
        total_count:
          type: integer
      required:
      - conflict_count
      - conflicts_by_type
      - ds_count
      - error_count
      - frame_count
      - frame_share
      - gt_count
      - total_count
      - valid_count
      - warning_count
    QualityReportTarget:
      enum:
      - job
      - task
      type: string
      description: |-
        * `job` - JOB
        * `task` - TASK
    QualitySettings:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        task_id:
          type: integer
          readOnly: true
        iou_threshold:
          type: number
          format: double
          description: Used for distinction between matched / unmatched shapes
        oks_sigma:
          type: number
          format: double
          description: |
            Like IoU threshold, but for points.
            The percent of the bbox area, used as the radius of the circle around the GT point,
            where the checked point is expected to be.
            Read more: https://cocodataset.org/#keypoints-eval
        line_thickness:
          type: number
          format: double
          description: |
            Thickness of polylines, relatively to the (image area) ^ 0.5.
            The distance to the boundary around the GT line,
            inside of which the checked line points should be
        low_overlap_threshold:
          type: number
          format: double
          description: |
            Used for distinction between strong / weak (low_overlap) matches
        compare_line_orientation:
          type: boolean
          description: Enables or disables polyline orientation comparison
        line_orientation_threshold:
          type: number
          format: double
          description: |
            The minimal gain in the GT IoU between the given and reversed line directions
            to consider the line inverted.
            Only used when the 'compare_line_orientation' parameter is true
        compare_groups:
          type: boolean
          description: Enables or disables annotation group checks
        group_match_threshold:
          type: number
          format: double
          description: |
            Minimal IoU for groups to be considered matching.
            Only used when the 'compare_groups' parameter is true
        check_covered_annotations:
          type: boolean
          description: |
            Check for partially-covered annotations, useful in segmentation tasks
        object_visibility_threshold:
          type: number
          format: double
          description: |
            Minimal visible area percent of the spatial annotations (polygons, masks)
            for reporting covered annotations.
            Only used when the 'object_visibility_threshold' parameter is true
        panoptic_comparison:
          type: boolean
          description: |
            Use only the visible part of the masks and polygons in comparisons
        compare_attributes:
          type: boolean
          description: Enables or disables annotation attribute comparison
    RegisterSerializerEx:
      type: object
      properties:
        username:
          type: string
          maxLength: 150
          minLength: 5
        email:
          type: string
          format: email
        first_name:
          type: string
        last_name:
          type: string
        email_verification_required:
          type: boolean
          readOnly: true
        key:
          type: string
          nullable: true
          readOnly: true
      required:
      - username
    RegisterSerializerExRequest:
      type: object
      properties:
        username:
          type: string
          minLength: 5
          maxLength: 150
        email:
          type: string
          format: email
          minLength: 1
        password1:
          type: string
          writeOnly: true
          minLength: 1
        password2:
          type: string
          writeOnly: true
          minLength: 1
        first_name:
          type: string
          minLength: 1
        last_name:
          type: string
          minLength: 1
      required:
      - password1
      - password2
      - username
    RestAuthDetail:
      type: object
      properties:
        detail:
          type: string
          readOnly: true
    RoleEnum:
      enum:
      - worker
      - supervisor
      - maintainer
      - owner
      type: string
      description: |-
        * `worker` - Worker
        * `supervisor` - Supervisor
        * `maintainer` - Maintainer
        * `owner` - Owner
    RqId:
      type: object
      properties:
        rq_id:
          type: string
          description: Request id
      required:
      - rq_id
    RqStatus:
      type: object
      properties:
        state:
          $ref: '#/components/schemas/RqStatusStateEnum'
        message:
          type: string
          default: ''
        progress:
          type: number
          format: double
          maximum: 100
          default: 0.0
      required:
      - state
    RqStatusStateEnum:
      enum:
      - Queued
      - Started
      - Finished
      - Failed
      type: string
      description: |-
        * `Queued` - Queued
        * `Started` - Started
        * `Finished` - Finished
        * `Failed` - Failed
    SeverityEnum:
      enum:
      - warning
      - error
      type: string
      description: |-
        * `warning` - WARNING
        * `error` - ERROR
    ShapeType:
      enum:
      - rectangle
      - polygon
      - polyline
      - points
      - ellipse
      - cuboid
      - mask
      - skeleton
      type: string
      description: |-
        * `rectangle` - RECTANGLE
        * `polygon` - POLYGON
        * `polyline` - POLYLINE
        * `points` - POINTS
        * `ellipse` - ELLIPSE
        * `cuboid` - CUBOID
        * `mask` - MASK
        * `skeleton` - SKELETON
    SigningRequest:
      type: object
      properties:
        url:
          type: string
          minLength: 1
      required:
      - url
    SortingMethod:
      enum:
      - lexicographical
      - natural
      - predefined
      - random
      type: string
      description: |-
        * `lexicographical` - LEXICOGRAPHICAL
        * `natural` - NATURAL
        * `predefined` - PREDEFINED
        * `random` - RANDOM
    Storage:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        location:
          $ref: '#/components/schemas/LocationEnum'
        cloud_storage_id:
          type: integer
          nullable: true
    StorageMethod:
      enum:
      - cache
      - file_system
      type: string
      description: |-
        * `cache` - CACHE
        * `file_system` - FILE_SYSTEM
    StorageRequest:
      type: object
      properties:
        location:
          $ref: '#/components/schemas/LocationEnum'
        cloud_storage_id:
          type: integer
          nullable: true
    StorageType:
      enum:
      - cloud_storage
      - local
      - share
      type: string
      description: |-
        * `cloud_storage` - CLOUD_STORAGE
        * `local` - LOCAL
        * `share` - SHARE
    SubLabeledShape:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/ShapeType'
        occluded:
          type: boolean
          default: false
        outside:
          type: boolean
          default: false
        z_order:
          type: integer
          default: 0
        rotation:
          type: number
          format: double
          maximum: 360
          minimum: 0
          default: 0.0
        points:
          type: array
          items:
            type: number
            format: double
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        label_id:
          type: integer
          minimum: 0
        group:
          type: integer
          minimum: 0
          nullable: true
        source:
          type: string
          default: manual
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeVal'
          default: []
      required:
      - frame
      - label_id
      - type
    SubLabeledShapeRequest:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/ShapeType'
        occluded:
          type: boolean
          default: false
        outside:
          type: boolean
          default: false
        z_order:
          type: integer
          default: 0
        rotation:
          type: number
          format: double
          maximum: 360
          minimum: 0
          default: 0.0
        points:
          type: array
          items:
            type: number
            format: double
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        label_id:
          type: integer
          minimum: 0
        group:
          type: integer
          minimum: 0
          nullable: true
        source:
          type: string
          minLength: 1
          default: manual
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeValRequest'
          default: []
      required:
      - frame
      - label_id
      - type
    SubLabeledTrack:
      type: object
      properties:
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        label_id:
          type: integer
          minimum: 0
        group:
          type: integer
          minimum: 0
          nullable: true
        source:
          type: string
          default: manual
        shapes:
          type: array
          items:
            $ref: '#/components/schemas/TrackedShape'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeVal'
          default: []
      required:
      - frame
      - label_id
      - shapes
    SubLabeledTrackRequest:
      type: object
      properties:
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        label_id:
          type: integer
          minimum: 0
        group:
          type: integer
          minimum: 0
          nullable: true
        source:
          type: string
          minLength: 1
          default: manual
        shapes:
          type: array
          items:
            $ref: '#/components/schemas/TrackedShapeRequest'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeValRequest'
          default: []
      required:
      - frame
      - label_id
      - shapes
    Sublabel:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          maxLength: 64
        color:
          type: string
          description: The hex value for the RGB color. Will be generated automatically,
            unless specified explicitly.
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/Attribute'
          default: []
          description: The list of attributes. If you want to remove an attribute,
            you need to recreate the label and specify the remaining attributes.
        type:
          type: string
          description: Associated annotation type for this label
        has_parent:
          type: boolean
      required:
      - name
    SublabelMappingEntryRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
        attributes:
          type: object
          additionalProperties:
            type: string
            minLength: 1
      required:
      - name
    SublabelRequest:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          minLength: 1
          maxLength: 64
        color:
          type: string
          description: The hex value for the RGB color. Will be generated automatically,
            unless specified explicitly.
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeRequest'
          default: []
          description: The list of attributes. If you want to remove an attribute,
            you need to recreate the label and specify the remaining attributes.
        type:
          type: string
          description: Associated annotation type for this label
        has_parent:
          type: boolean
      required:
      - name
    TaskAnnotationsUpdateRequest:
      oneOf:
      - $ref: '#/components/schemas/LabeledDataRequest'
      - $ref: '#/components/schemas/AnnotationFileRequest'
      nullable: true
    TaskAnnotationsWriteRequest:
      oneOf:
      - $ref: '#/components/schemas/AnnotationFileRequest'
      nullable: true
    TaskFileRequest:
      type: object
      properties:
        task_file:
          type: string
          format: binary
      required:
      - task_file
    TaskRead:
      type: object
      properties:
        url:
          type: string
          format: uri
          readOnly: true
        id:
          type: integer
          readOnly: true
        name:
          type: string
          readOnly: true
        project_id:
          type: integer
          nullable: true
        mode:
          type: string
          readOnly: true
        owner:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          nullable: true
        assignee:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          nullable: true
        bug_tracker:
          type: string
          readOnly: true
        created_date:
          type: string
          format: date-time
          readOnly: true
        updated_date:
          type: string
          format: date-time
          readOnly: true
        overlap:
          type: integer
          readOnly: true
          nullable: true
        segment_size:
          type: integer
          readOnly: true
        status:
          allOf:
          - $ref: '#/components/schemas/JobStatus'
          readOnly: true
        data_chunk_size:
          type: integer
          maximum: 2147483647
          minimum: 0
          nullable: true
          readOnly: true
        data_compressed_chunk_type:
          allOf:
          - $ref: '#/components/schemas/ChunkType'
          readOnly: true
        guide_id:
          type: integer
          nullable: true
        data_original_chunk_type:
          allOf:
          - $ref: '#/components/schemas/ChunkType'
          readOnly: true
        size:
          type: integer
          maximum: 2147483647
          minimum: 0
          readOnly: true
        image_quality:
          type: integer
          maximum: 32767
          minimum: 0
          readOnly: true
        data:
          type: integer
          readOnly: true
        dimension:
          type: string
        subset:
          type: string
          readOnly: true
        organization:
          type: integer
          readOnly: true
          nullable: true
        target_storage:
          allOf:
          - $ref: '#/components/schemas/Storage'
          nullable: true
        source_storage:
          allOf:
          - $ref: '#/components/schemas/Storage'
          nullable: true
        jobs:
          $ref: '#/components/schemas/JobsSummary'
        labels:
          $ref: '#/components/schemas/LabelsSummary'
      required:
      - jobs
      - labels
    TaskWriteRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 256
        project_id:
          type: integer
          nullable: true
        owner_id:
          type: integer
          writeOnly: true
          nullable: true
        assignee_id:
          type: integer
          writeOnly: true
          nullable: true
        bug_tracker:
          type: string
          maxLength: 2000
        overlap:
          type: integer
          maximum: 2147483647
          minimum: 0
          nullable: true
        segment_size:
          type: integer
          maximum: 2147483647
          minimum: 0
        labels:
          type: array
          items:
            $ref: '#/components/schemas/PatchedLabelRequest'
        subset:
          type: string
          maxLength: 64
        target_storage:
          allOf:
          - $ref: '#/components/schemas/StorageRequest'
          nullable: true
        source_storage:
          allOf:
          - $ref: '#/components/schemas/StorageRequest'
          nullable: true
      required:
      - name
    TasksSummary:
      type: object
      properties:
        count:
          type: integer
          default: 0
        url:
          type: string
          format: uri
          readOnly: true
    Token:
      type: object
      description: Serializer for Token model.
      properties:
        key:
          type: string
          maxLength: 40
      required:
      - key
    TrackedShape:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/ShapeType'
        occluded:
          type: boolean
          default: false
        outside:
          type: boolean
          default: false
        z_order:
          type: integer
          default: 0
        rotation:
          type: number
          format: double
          maximum: 360
          minimum: 0
          default: 0.0
        points:
          type: array
          items:
            type: number
            format: double
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeVal'
          default: []
      required:
      - frame
      - type
    TrackedShapeRequest:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/ShapeType'
        occluded:
          type: boolean
          default: false
        outside:
          type: boolean
          default: false
        z_order:
          type: integer
          default: 0
        rotation:
          type: number
          format: double
          maximum: 360
          minimum: 0
          default: 0.0
        points:
          type: array
          items:
            type: number
            format: double
        id:
          type: integer
          nullable: true
        frame:
          type: integer
          minimum: 0
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeValRequest'
          default: []
      required:
      - frame
      - type
    Transformation:
      type: object
      properties:
        name:
          type: string
        binary:
          allOf:
          - $ref: '#/components/schemas/BinaryOperation'
          nullable: true
      required:
      - name
    User:
      type: object
      properties:
        url:
          type: string
          format: uri
          readOnly: true
        id:
          type: integer
          readOnly: true
        username:
          type: string
          description: Required. 150 characters or fewer. Letters, digits and @/./+/-/_
            only.
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          maxLength: 150
        last_name:
          type: string
          maxLength: 150
        email:
          type: string
          format: email
          title: Email address
          maxLength: 254
        groups:
          type: array
          items:
            type: string
        is_staff:
          type: boolean
          title: Staff status
          description: Designates whether the user can log into this admin site.
        is_superuser:
          type: boolean
          title: Superuser status
          description: Designates that this user has all permissions without explicitly
            assigning them.
        is_active:
          type: boolean
          title: Active
          description: Designates whether this user should be treated as active. Unselect
            this instead of deleting accounts.
        last_login:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        date_joined:
          type: string
          format: date-time
          readOnly: true
      required:
      - groups
      - username
    WebhookContentType:
      enum:
      - application/json
      type: string
      description: '* `application/json` - JSON'
    WebhookDeliveryRead:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        webhook_id:
          type: integer
          readOnly: true
        event:
          type: string
          readOnly: true
        status_code:
          type: integer
          readOnly: true
          nullable: true
        redelivery:
          type: boolean
          readOnly: true
        created_date:
          type: string
          format: date-time
          readOnly: true
        updated_date:
          type: string
          format: date-time
          readOnly: true
        changed_fields:
          type: string
          readOnly: true
        request:
          type: object
          readOnly: true
        response:
          type: object
          readOnly: true
    WebhookRead:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        url:
          type: string
          format: uri
          readOnly: true
        target_url:
          type: string
          format: uri
          readOnly: true
        description:
          type: string
          readOnly: true
        type:
          $ref: '#/components/schemas/WebhookType'
        content_type:
          $ref: '#/components/schemas/WebhookContentType'
        is_active:
          type: boolean
          readOnly: true
        enable_ssl:
          type: boolean
          readOnly: true
        created_date:
          type: string
          format: date-time
          readOnly: true
        updated_date:
          type: string
          format: date-time
          readOnly: true
        owner:
          allOf:
          - $ref: '#/components/schemas/BasicUser'
          readOnly: true
          nullable: true
        project_id:
          type: integer
          nullable: true
        organization:
          type: integer
          readOnly: true
          nullable: true
        events:
          type: array
          items:
            $ref: '#/components/schemas/EventsEnum'
          readOnly: true
        last_status:
          type: integer
          readOnly: true
        last_delivery_date:
          type: string
          format: date-time
          readOnly: true
      required:
      - content_type
      - type
    WebhookType:
      enum:
      - organization
      - project
      type: string
      description: |-
        * `organization` - ORGANIZATION
        * `project` - PROJECT
    WebhookWriteRequest:
      type: object
      properties:
        target_url:
          type: string
          format: uri
          minLength: 1
          maxLength: 8192
        description:
          type: string
          maxLength: 128
        type:
          $ref: '#/components/schemas/WebhookType'
        content_type:
          $ref: '#/components/schemas/WebhookContentType'
        secret:
          type: string
          maxLength: 64
        is_active:
          type: boolean
        enable_ssl:
          type: boolean
        project_id:
          type: integer
          writeOnly: true
          nullable: true
        events:
          type: array
          items:
            $ref: '#/components/schemas/EventsEnum'
          writeOnly: true
      required:
      - events
      - target_url
      - type
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
    csrfAuth:
      type: apiKey
      in: cookie
      name: csrftoken
      description: Can be sent as a cookie or as the X-CSRFTOKEN header
    sessionAuth:
      type: apiKey
      in: cookie
      name: sessionid
    signatureAuth:
      type: apiKey
      in: query
      name: sign
      description: Can be used to share URLs to private links
    tokenAuth:
      type: apiKey
      in: header
      name: Authorization
      description: |2

        To authenticate using a token (or API key), you need to have 3 components in a request:
        - the 'sessionid' cookie
        - the 'csrftoken' cookie or 'X-CSRFTOKEN' header
        - the 'Authentication' header with the 'Token ' prefix

        You can obtain an API key (the token) from the server response on
        the basic auth request.
      x-token-prefix: Token
externalDocs:
  description: CVAT documentation
  url: https://docs.cvat.ai/docs/
