name: ESLint
on: pull_request
jobs:
  Linter:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
            node-version: '16.x'

      - name: Install dependencies
        run: |
          yarn install --frozen-lockfile
          (cd tests && yarn install --frozen-lockfile)

      - name: Run checks
        run: |
          echo "ESLint version: "$(yarn run -s eslint --version)
          yarn run eslint .
