# Workflow deletes image artifacts that created by CI workflow
name: Delete image artifacts
on:
  workflow_run:
    workflows: [CI, Comment, Full]
    types:
      - completed

jobs:
  cleanup:
    runs-on: ubuntu-latest
    env:
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Clean up
        run: |
          wri=${{ github.event.workflow_run.id }}
          for ai in $(gh api /repos/${{ github.repository }}/actions/runs/$wri/artifacts | jq '.artifacts[] | select( .name | startswith("cvat"))  | .id');
          do
            gh api --method DELETE /repos/${{ github.repository }}/actions/artifacts/$ai
          done
