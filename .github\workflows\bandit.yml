name: Bandit
on: pull_request
jobs:
  Linter:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - id: files
        uses: tj-actions/changed-files@v41.0.0
        with:
          files: |
            **/*.py
          files_ignore: |
            **/cvat-sdk/*

      - name: Run checks
        run: |
          CHANGED_FILES="${{steps.files.outputs.all_changed_files}}"

          if [[ ! -z $CHANGED_FILES ]]; then
            pipx install bandit

            echo "Bandit version: "$(bandit --version | head -1)
            echo "The files will be checked: "$(echo $CHANGED_FILES)
            bandit -a file --ini .bandit $CHANGED_FILES
          else
            echo "No files with the \"py\" extension found"
          fi
