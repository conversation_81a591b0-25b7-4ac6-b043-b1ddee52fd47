# SHA1:27238f2f377debba1b5fe910878f0cc0cfaf6e7d
#
# This file is autogenerated by pip-compile-multi
# To update, run:
#
#    pip-compile-multi
#
-r base.txt
astroid==2.11.7
    # via pylint
autopep8==2.1.0
    # via django-silk
black==24.3.0
    # via -r cvat/requirements/development.in
dill==0.3.8
    # via pylint
django-extensions==3.0.8
    # via -r cvat/requirements/development.in
django-silk==5.0.3
    # via -r cvat/requirements/development.in
gprof2dot==2022.7.29
    # via django-silk
isort==5.13.2
    # via pylint
lazy-object-proxy==1.10.0
    # via astroid
mccabe==0.7.0
    # via pylint
mypy-extensions==1.0.0
    # via black
pathspec==0.12.1
    # via black
platformdirs==4.2.0
    # via
    #   black
    #   pylint
pycodestyle==2.11.1
    # via autopep8
pylint==2.14.5
    # via
    #   -r cvat/requirements/development.in
    #   pylint-django
    #   pylint-plugin-utils
pylint-django==2.5.3
    # via -r cvat/requirements/development.in
pylint-plugin-utils==0.7
    # via
    #   -r cvat/requirements/development.in
    #   pylint-django
rope==0.17.0
    # via -r cvat/requirements/development.in
snakeviz==2.1.0
    # via -r cvat/requirements/development.in
tomli==2.0.1
    # via
    #   autopep8
    #   black
    #   pylint
tomlkit==0.12.4
    # via pylint
tornado==6.4
    # via snakeviz

# The following packages are considered to be unsafe in a requirements file:
setuptools==69.2.0
    # via astroid
