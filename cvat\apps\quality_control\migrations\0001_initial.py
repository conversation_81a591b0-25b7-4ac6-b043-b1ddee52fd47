# Generated by Django 4.2.1 on 2023-06-08 12:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("engine", "0070_add_job_type_created_date"),
    ]

    operations = [
        migrations.CreateModel(
            name="AnnotationConflict",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("frame", models.PositiveIntegerField()),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("missing_annotation", "MISSING_ANNOTATION"),
                            ("extra_annotation", "EXTRA_ANNOTATION"),
                            ("mismatching_label", "MISMATCHING_LABEL"),
                            ("low_overlap", "LOW_OVERLAP"),
                            ("mismatching_direction", "MISMATCHING_DIRECTION"),
                            ("mismatching_attributes", "MISMATCHING_ATTRIBUTES"),
                            ("mismatching_groups", "MISMATCHING_GROUPS"),
                            ("covered_annotation", "COVERED_ANNOTATION"),
                        ],
                        max_length=32,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[("warning", "WARNING"), ("error", "ERROR")], max_length=32
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="QualitySettings",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("iou_threshold", models.FloatField()),
                ("oks_sigma", models.FloatField()),
                ("line_thickness", models.FloatField()),
                ("low_overlap_threshold", models.FloatField()),
                ("compare_line_orientation", models.BooleanField()),
                ("line_orientation_threshold", models.FloatField()),
                ("compare_groups", models.BooleanField()),
                ("group_match_threshold", models.FloatField()),
                ("check_covered_annotations", models.BooleanField()),
                ("object_visibility_threshold", models.FloatField()),
                ("panoptic_comparison", models.BooleanField()),
                ("compare_attributes", models.BooleanField()),
                (
                    "task",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="quality_settings",
                        to="engine.task",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="QualityReport",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                ("target_last_updated", models.DateTimeField()),
                ("gt_last_updated", models.DateTimeField()),
                ("data", models.JSONField()),
                (
                    "job",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="quality_reports",
                        to="engine.job",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="quality_control.qualityreport",
                    ),
                ),
                (
                    "task",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="quality_reports",
                        to="engine.task",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="AnnotationId",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("obj_id", models.PositiveIntegerField()),
                ("job_id", models.PositiveIntegerField()),
                (
                    "type",
                    models.CharField(
                        choices=[("tag", "TAG"), ("shape", "SHAPE"), ("track", "TRACK")],
                        max_length=32,
                    ),
                ),
                (
                    "shape_type",
                    models.CharField(
                        choices=[
                            ("rectangle", "RECTANGLE"),
                            ("polygon", "POLYGON"),
                            ("polyline", "POLYLINE"),
                            ("points", "POINTS"),
                            ("ellipse", "ELLIPSE"),
                            ("cuboid", "CUBOID"),
                            ("mask", "MASK"),
                            ("skeleton", "SKELETON"),
                        ],
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "conflict",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="annotation_ids",
                        to="quality_control.annotationconflict",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="annotationconflict",
            name="report",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="conflicts",
                to="quality_control.qualityreport",
            ),
        ),
    ]
