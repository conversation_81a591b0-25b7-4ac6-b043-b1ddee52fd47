{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 90, "gradientMode": "scheme", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"builderOptions": {"fields": [], "filters": [], "metrics": [{"aggregation": "count", "field": "*"}], "mode": "trend", "orderBy": [], "table": "events", "timeField": "timestamp", "timeFieldType": "DateTime64(3, 'Etc/UTC')"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "format": 0, "meta": {"builderOptions": {"fields": [], "filters": [], "metrics": [{"aggregation": "count", "field": "*"}], "mode": "trend", "orderBy": [], "table": "events", "timeField": "timestamp", "timeFieldType": "DateTime64(3, 'Etc/UTC')"}}, "queryType": "sql", "rawSql": "SELECT $__timeInterval(timestamp) as time, count(*)\r\nFROM events\r\nWHERE $__timeFilter(timestamp)\r\nAND scope IN (${scopes})\r\nAND source IN (${sources})\r\nAND (-1 IN (${users}) OR user_id IN (${users}))\r\nAND (-1 IN (${organizations}) OR org_id IN (${organizations}))\r\nAND (-1 IN (${projects}) OR project_id IN (${projects}))\r\nAND (-1 IN (${tasks}) OR task_id IN (${tasks}))\r\nAND (-1 IN (${jobs}) OR job_id IN (${jobs}))\r\nGROUP BY time ORDER BY time ASC", "refId": "A"}], "title": "Overall Activity", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 158}]}]}, "gridPos": {"h": 23, "w": 24, "x": 0, "y": 7}, "id": 4, "options": {"footer": {"enablePagination": true, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.3.6", "targets": [{"builderOptions": {"fields": ["*"], "filters": [{"condition": "AND", "filterType": "custom", "key": "timestamp", "operator": "WITH IN DASHBOARD TIME RANGE", "type": "DateTime64(3, 'Etc/UTC')", "value": "TODAY"}, {"condition": "AND", "filterType": "custom", "key": "scope", "operator": "IN", "type": "String", "value": [""]}], "mode": "list", "orderBy": [{"dir": "ASC", "name": "timestamp"}], "table": "events"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "format": 1, "meta": {"builderOptions": {"fields": ["*"], "filters": [{"condition": "AND", "filterType": "custom", "key": "timestamp", "operator": "WITH IN DASHBOARD TIME RANGE", "type": "DateTime64(3, 'Etc/UTC')", "value": "TODAY"}, {"condition": "AND", "filterType": "custom", "key": "scope", "operator": "IN", "type": "String", "value": [""]}], "mode": "list", "orderBy": [{"dir": "ASC", "name": "timestamp"}], "table": "events"}}, "queryType": "sql", "rawSql": "SELECT * \r\nFROM events \r\nWHERE $__timeFilter(timestamp)\r\n    AND scope IN (${scopes})\r\n    AND source IN (${sources})\r\n    AND (-1 IN (${users}) OR user_id IN (${users}))\r\n    AND (-1 IN (${organizations}) OR org_id IN (${organizations}))\r\n    AND (-1 IN (${projects}) OR project_id IN (${projects}))\r\n    AND (-1 IN (${tasks}) OR task_id IN (${tasks}))\r\n    AND (-1 IN (${jobs}) OR job_id IN (${jobs}))\r\nORDER BY timestamp DESC\r\nLIMIT 1000", "refId": "A"}], "title": "All events", "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 0, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 0, "y": 30}, "id": 6, "options": {"barRadius": 0, "barWidth": 0.51, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "orientation": "horizontal", "showValue": "always", "stacking": "none", "tooltip": {"mode": "single", "sort": "none"}, "xField": "browser", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "9.3.6", "targets": [{"builderOptions": {"fields": [], "filters": [], "limit": 100, "mode": "list", "orderBy": [], "table": "events"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "format": 1, "meta": {"builderOptions": {"fields": [], "filters": [], "limit": 100, "mode": "list", "orderBy": [], "table": "events"}}, "queryType": "sql", "rawSql": "SELECT\r\n    browser,\r\n    count()\r\nFROM\r\n(\r\n    SELECT\r\n        concat(JSON_VALUE(payload, '$.platform.name'), ' ', JSON_VALUE(payload, '$.platform.version')) AS browser,\r\n        user_id\r\n    FROM cvat.events\r\n    WHERE  $__timeFilter(timestamp) AND (scope = 'load:cvat') AND (browser != ' ')\r\n    GROUP BY\r\n        user_id,\r\n        browser\r\n)\r\nGROUP BY browser\r\nORDER BY count() DESC", "refId": "A"}], "title": "Browser", "type": "barchart"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 0, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 30}, "id": 8, "options": {"barRadius": 0, "barWidth": 0.51, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "orientation": "horizontal", "showValue": "always", "stacking": "none", "tooltip": {"mode": "single", "sort": "none"}, "xField": "os", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "9.3.6", "targets": [{"builderOptions": {"fields": [], "filters": [], "limit": 100, "mode": "list", "orderBy": [], "table": "events"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "format": 1, "meta": {"builderOptions": {"fields": [], "filters": [], "limit": 100, "mode": "list", "orderBy": [], "table": "events"}}, "queryType": "sql", "rawSql": "SELECT\r\n    os,\r\n    count()\r\nFROM\r\n(\r\n    SELECT\r\n        JSON_VALUE(payload, '$.platform.os') AS os,\r\n        user_id\r\n    FROM cvat.events\r\n    WHERE  $__timeFilter(timestamp) AND (scope = 'load:cvat') AND (os != '')\r\n    GROUP BY\r\n        user_id,\r\n        os\r\n)\r\nGROUP BY os\r\nORDER BY count() DESC", "refId": "A"}], "title": "OS", "type": "barchart"}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"allValue": "", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "definition": "SELECT DISTINCT scope\nFROM events\nWHERE $__timeFilter(timestamp)", "hide": 0, "includeAll": true, "label": "<PERSON><PERSON>", "multi": true, "name": "scopes", "options": [], "query": "SELECT DISTINCT scope\nFROM events\nWHERE $__timeFilter(timestamp)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"allValue": "", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "definition": "SELECT DISTINCT source\nFROM events\nWHERE $__timeFilter(timestamp)", "hide": 0, "includeAll": true, "label": "Source", "multi": true, "name": "sources", "options": [], "query": "SELECT DISTINCT source\nFROM events\nWHERE $__timeFilter(timestamp)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": "-1", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "definition": "SELECT DISTINCT user_id\nFROM events\nWHERE $__timeFilter(timestamp)\n  AND user_id IS NOT NULL", "hide": 0, "includeAll": true, "label": "User", "multi": true, "name": "users", "options": [], "query": "SELECT DISTINCT user_id\nFROM events\nWHERE $__timeFilter(timestamp)\n  AND user_id IS NOT NULL", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": "-1", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "definition": "SELECT DISTINCT project_id\nFROM events\nWHERE  $__timeFilter(timestamp)\n  AND project_id IS NOT NULL", "hide": 0, "includeAll": true, "label": "Project", "multi": true, "name": "projects", "options": [], "query": "SELECT DISTINCT project_id\nFROM events\nWHERE  $__timeFilter(timestamp)\n  AND project_id IS NOT NULL", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": "-1", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "definition": "SELECT DISTINCT task_id\nFROM events\nWHERE $__timeFilter(timestamp) \n  AND task_id IS NOT NULL", "hide": 0, "includeAll": true, "label": "Task", "multi": true, "name": "tasks", "options": [], "query": "SELECT DISTINCT task_id\nFROM events\nWHERE $__timeFilter(timestamp) \n  AND task_id IS NOT NULL", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": "-1", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "definition": "SELECT DISTINCT job_id\nFROM events\nWHERE $__timeFilter(timestamp)\n  AND job_id IS NOT NULL", "hide": 0, "includeAll": true, "label": "Job", "multi": true, "name": "jobs", "options": [], "query": "SELECT DISTINCT job_id\nFROM events\nWHERE $__timeFilter(timestamp)\n  AND job_id IS NOT NULL", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": "-1", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "PDEE91DDB90597936"}, "definition": "SELECT DISTINCT org_id\nFROM events\nWHERE $__timeFilter(timestamp)\nAND org_id IS NOT NULL", "hide": 0, "includeAll": true, "label": "Organization", "multi": true, "name": "organizations", "options": [], "query": "SELECT DISTINCT org_id\nFROM events\nWHERE $__timeFilter(timestamp)\nAND org_id IS NOT NULL", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "All events", "uid": "EIGSTDAVz", "version": 1, "weekStart": ""}