name: HadoLint
on: pull_request
jobs:
  Linter:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - id: files
        uses: tj-actions/changed-files@v41.0.0
        with:
          files: |
              **/Dockerfile*

      - name: Run checks
        env:
          HADOLINT: "${{ github.workspace }}/hadolint"
          HADOLINT_VER: "2.12.0"
          VERIFICATION_LEVEL: "error"
        run: |
          CHANGED_FILES="${{steps.files.outputs.all_changed_files}}"

          if [[ ! -z $CHANGED_FILES ]]; then
            curl -sL -o $HADOLINT "https://github.com/hadolint/hadolint/releases/download/v$HADOLINT_VER/hadolint-Linux-x86_64" && chmod 700 $HADOLINT
            echo "HadoLint version: "$($HADOLINT --version)
            echo "The files will be checked: "$(echo $CHANGED_FILES)

            $HADOLINT -t $VERIFICATION_LEVEL $CHANGED_FILES
          else
            echo "No files with the \"Dockerfile*\" name found"
          fi
