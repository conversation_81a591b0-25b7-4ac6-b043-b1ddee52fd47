{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "REST API tests: Attach to server",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "host": "127.0.0.1",
                "port": 9090
            },
            "pathMappings": [
                {
                    "localRoot": "${workspaceFolder}",
                    "remoteRoot": "/home/<USER>/"
                },
                {
                    "localRoot": "${workspaceFolder}/.env",
                    "remoteRoot": "/opt/venv",
                }
            ],
            "justMyCode": false,
        },
        {
            "name": "REST API tests: Attach to RQ annotation worker",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "host": "127.0.0.1",
                "port": 9091
            },
            "pathMappings": [
                {
                    "localRoot": "${workspaceFolder}",
                    "remoteRoot": "/home/<USER>/"
                },
                {
                    "localRoot": "${workspaceFolder}/.env",
                    "remoteRoot": "/opt/venv",
                }
            ],
            "justMyCode": false,
        },
        {
            "name": "REST API tests: Attach to RQ export worker",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "host": "127.0.0.1",
                "port": 9092
            },
            "pathMappings": [
                {
                    "localRoot": "${workspaceFolder}",
                    "remoteRoot": "/home/<USER>/"
                },
                {
                    "localRoot": "${workspaceFolder}/.env",
                    "remoteRoot": "/opt/venv",
                }
            ],
            "justMyCode": false,
        },
        {
            "name": "REST API tests: Attach to RQ import worker",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "host": "127.0.0.1",
                "port": 9093
            },
            "pathMappings": [
                {
                    "localRoot": "${workspaceFolder}",
                    "remoteRoot": "/home/<USER>/"
                },
                {
                    "localRoot": "${workspaceFolder}/.env",
                    "remoteRoot": "/opt/venv",
                }
            ],
            "justMyCode": false,
        },
        {
            "name": "REST API tests: Attach to RQ quality reports worker",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "host": "127.0.0.1",
                "port": 9094
            },
            "pathMappings": [
                {
                    "localRoot": "${workspaceFolder}",
                    "remoteRoot": "/home/<USER>/"
                },
                {
                    "localRoot": "${workspaceFolder}/.env",
                    "remoteRoot": "/opt/venv",
                }
            ],
            "justMyCode": false,
        },
        {
            "name": "REST API tests: Attach to RQ analytics reports worker",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "host": "127.0.0.1",
                "port": 9095
            },
            "pathMappings": [
                {
                    "localRoot": "${workspaceFolder}",
                    "remoteRoot": "/home/<USER>/"
                },
                {
                    "localRoot": "${workspaceFolder}/.env",
                    "remoteRoot": "/opt/venv",
                }
            ],
            "justMyCode": false,
        },
        {
            "type": "pwa-chrome",
            "request": "launch",
            "preLaunchTask": "npm: start - cvat-ui",
            "name": "ui.js: debug",
            "url": "http://localhost:3000",
            "webRoot": "${workspaceFolder}/cvat-ui",
            "sourceMaps": true,
            "sourceMapPathOverrides": {
                "webpack://cvat/./*": "${workspaceFolder}/cvat-core/*",
                "webpack:///./*": "${webRoot}/*",
                "webpack:///src/*": "${webRoot}/*",
                "webpack:///*": "*",
                "webpack:///./~/*": "${webRoot}/node_modules/*"
            },
            "smartStep": true,
        },
        {
            "type": "node",
            "request": "launch",
            "name": "ui.js: test",
            "cwd": "${workspaceRoot}/tests",
            "runtimeExecutable": "${workspaceRoot}/tests/node_modules/.bin/cypress",
            "args": [
                "run",
                "--headless",
                "--browser",
                "chrome"
            ],
            "outputCapture": "std",
            "console": "internalConsole"
        },
        {
            "name": "server: django",
            "type": "debugpy",
            "request": "launch",
            "stopOnEntry": false,
            "justMyCode": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/manage.py",
            "env": {
                "CVAT_SERVERLESS": "1",
                "ALLOWED_HOSTS": "*",
                "DJANGO_LOG_SERVER_HOST": "localhost",
                "DJANGO_LOG_SERVER_PORT": "8282"
            },
            "args": [
                "runserver",
                "--noreload",
                "--insecure",
                "127.0.0.1:7000"
            ],
            "django": true,
            "cwd": "${workspaceFolder}",
            "console": "internalConsole"
        },
        {
            "name": "server: chrome",
            "type": "pwa-chrome",
            "request": "launch",
            "url": "http://localhost:7000/",
            "disableNetworkCache": true,
            "trace": true,
            "showAsyncStacks": true,
            "pathMapping": {
                "/static/engine/": "${workspaceFolder}/cvat/apps/engine/static/engine/",
                "/static/dashboard/": "${workspaceFolder}/cvat/apps/dashboard/static/dashboard/",
            }
        },
        {
            "name": "server: RQ - import",
            "type": "debugpy",
            "request": "launch",
            "stopOnEntry": false,
            "justMyCode": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/manage.py",
            "args": [
                "rqworker",
                "import",
                "--worker-class",
                "cvat.rqworker.SimpleWorker"
            ],
            "django": true,
            "cwd": "${workspaceFolder}",
            "env": {
                "DJANGO_LOG_SERVER_HOST": "localhost",
                "DJANGO_LOG_SERVER_PORT": "8282"
            },
            "console": "internalConsole"
        },
        {
            "name": "server: RQ - export",
            "type": "debugpy",
            "request": "launch",
            "stopOnEntry": false,
            "justMyCode": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/manage.py",
            "args": [
                "rqworker",
                "export",
                "--worker-class",
                "cvat.rqworker.SimpleWorker",
            ],
            "django": true,
            "cwd": "${workspaceFolder}",
            "env": {
                "DJANGO_LOG_SERVER_HOST": "localhost",
                "DJANGO_LOG_SERVER_PORT": "8282"
            },
            "console": "internalConsole"
        },
        {
            "name": "server: RQ - quality reports",
            "type": "debugpy",
            "request": "launch",
            "stopOnEntry": false,
            "justMyCode": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/manage.py",
            "args": [
                "rqworker",
                "quality_reports",
                "--worker-class",
                "cvat.rqworker.SimpleWorker",
            ],
            "django": true,
            "cwd": "${workspaceFolder}",
            "env": {
                "DJANGO_LOG_SERVER_HOST": "localhost",
                "DJANGO_LOG_SERVER_PORT": "8282"
            },
            "console": "internalConsole"
        },
        {
            "name": "server: RQ - analytics reports",
            "type": "debugpy",
            "request": "launch",
            "stopOnEntry": false,
            "justMyCode": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/manage.py",
            "args": [
                "rqworker",
                "analytics_reports",
                "--worker-class",
                "cvat.rqworker.SimpleWorker",
            ],
            "django": true,
            "cwd": "${workspaceFolder}",
            "env": {
                "DJANGO_LOG_SERVER_HOST": "localhost",
                "DJANGO_LOG_SERVER_PORT": "8282"
            },
            "console": "internalConsole"
        },
        {
            "name": "server: RQ - scheduler",
            "type": "debugpy",
            "request": "launch",
            "stopOnEntry": false,
            "justMyCode": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/rqscheduler.py",
            "django": true,
            "cwd": "${workspaceFolder}",
            "args": [
                "-i", "1"
            ],
            "env": {
                "DJANGO_LOG_SERVER_HOST": "localhost",
                "DJANGO_LOG_SERVER_PORT": "8282"
            },
            "console": "internalConsole"
        },
        {
            "name": "server: RQ - annotation",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/manage.py",
            "args": [
                "rqworker",
                "annotation",
                "--worker-class",
                "cvat.rqworker.SimpleWorker",
            ],
            "django": true,
            "cwd": "${workspaceFolder}",
            "env": {},
            "console": "internalConsole"
        },
        {
            "name": "server: RQ - webhooks",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/manage.py",
            "args": [
                "rqworker",
                "webhooks",
                "--worker-class",
                "cvat.rqworker.SimpleWorker",
            ],
            "django": true,
            "cwd": "${workspaceFolder}",
            "env": {},
            "console": "internalConsole"
        },
        {
            "name": "server: RQ - cleaning",
            "type": "debugpy",
            "request": "launch",
            "stopOnEntry": false,
            "justMyCode": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/manage.py",
            "args": [
                "rqworker",
                "cleaning",
                "--worker-class",
                "cvat.rqworker.SimpleWorker"
            ],
            "django": true,
            "cwd": "${workspaceFolder}",
            "env": {
                "DJANGO_LOG_SERVER_HOST": "localhost",
                "DJANGO_LOG_SERVER_PORT": "8282"
            },
            "console": "internalConsole"
        },
        {
            "name": "server: migrate",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/manage.py",
            "args": [
                "migrate"
            ],
            "django": true,
            "cwd": "${workspaceFolder}",
            "env": {},
            "console": "internalConsole"
        },
        {
            "name": "server: tests",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/manage.py",
            "args": [
                "test",
                "--settings",
                "cvat.settings.testing",
                "cvat/apps",
                "cvat-cli/"
            ],
            "django": true,
            "cwd": "${workspaceFolder}",
            "env": {},
            "console": "internalConsole"
        },
        {
            "name": "server: REST API tests",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "python": "${command:python.interpreterPath}",
            "module": "pytest",
            "args": [
                "tests/python/rest_api/"
            ],
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal"
        },
        {
            "name": "sdk: tests",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "python": "${command:python.interpreterPath}",
            "module": "pytest",
            "args": [
                "tests/python/sdk/"
            ],
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal"
        },
        {
            "name": "cli: tests",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "python": "${command:python.interpreterPath}",
            "module": "pytest",
            "args": [
                "tests/python/cli/"
            ],
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal"
        },
        {
            "name": "api client: Postprocess generator output",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceFolder}/cvat-sdk/gen/postprocess.py",
            "args": [
                "--schema", "${workspaceFolder}/cvat/schema.yml",
                "--input-path", "${workspaceFolder}/cvat-sdk/cvat_sdk/"
            ],
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal"
        },
        {
            "name": "docs: Postprocess SDK docs",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceFolder}/site/process_sdk_docs.py",
            "args": [
                "--input-dir", "${workspaceFolder}/cvat-sdk/docs/",
                "--site-root", "${workspaceFolder}/site/",
            ],
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal"
        },
        {
            "name": "docs: Build docs",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceFolder}/site/build_docs.py",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal"
        },
        {
            "name": "server: Generate REST API Schema",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "python": "${command:python.interpreterPath}",
            "program": "${workspaceRoot}/manage.py",
            "args": [
                "spectacular",
                "--file",
                "${workspaceFolder}/cvat/schema.yml"
            ],
            "django": true,
            "cwd": "${workspaceFolder}",
            "env": {},
            "console": "internalConsole"
        },
        {
            "name": "core.js: debug",
            "type": "node",
            "request": "launch",
            "cwd": "${workspaceFolder}/cvat-core",
            "runtimeExecutable": "node",
            "runtimeArgs": [
                "--nolazy",
                "--inspect-brk=9230",
                "src/api.js"
            ],
            "port": 9230
        },
        {
            "type": "node",
            "request": "launch",
            "name": "jest debug",
            "cwd": "${workspaceFolder}/cvat-core",
            "program": "${workspaceFolder}/node_modules/.bin/jest",
            "args": [
                "--config",
                "${workspaceFolder}/cvat-core/jest.config.cjs"
            ],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen",
            "disableOptimisticBPs": true,
        }
    ],
    "compounds": [
        {
            "name": "server: debug",
            "configurations": [
                "server: django",
                "server: RQ - import",
                "server: RQ - export",
                "server: RQ - annotation",
                "server: RQ - webhooks",
                "server: RQ - scheduler",
                "server: RQ - quality reports",
                "server: RQ - analytics reports",
                "server: RQ - cleaning"
            ]
        }
    ]
}
