{{- if .Values.analytics.enabled }}
apiVersion: v1
# clickhouse subchart helm only allows to use Secret for this purpose
# https://github.com/bitnami/charts/tree/main/bitnami/clickhouse#using-custom-scripts
kind: Secret
metadata:
  # clickhouse subchart helm doesn't allow template name
  name: cvat-clickhouse-init
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "cvat.labels" . | nindent 4 }}
    app: cvat-app
    tier: clickhouse
{{- if semverCompare ">=1.21-0" .Capabilities.KubeVersion.GitVersion }}
immutable: false
{{- end }}
data:
{{ (.Files.Glob "analytics/clickhouse/*.sh").AsSecrets | indent 2 }}
{{- end}}
